flutter run --debug                                                                                                         
mac@Macs-MacBook-Pro culture_connect % flutter run --debug
Launching lib/main.dart on iPhone 15 Pro Max in debug mode...
Running Xcode build...                                                  
 └─Compiling, linking and signing...                        58.3s
Xcode build done.                                           299.6s
no valid “aps-environment” entitlement string found for application
flutter: 🔥 Waiting for Firebase initialization...
flutter: 🚀 Starting app initialization
flutter: ❌ Error preloading asset assets/images/splash.png: Unable to load asset: "assets/images/splash.png".
flutter: The asset does not exist or has empty data.
flutter: ✅ Preloaded asset: assets/animations/splash_animation.json (3741 bytes)
flutter: ✅ Critical assets preloaded in 32ms
flutter: ✅ SharedPreferences initialized in 221ms
flutter: ✅ Firebase core initialized in 701ms
flutter: ✅ App initialization completed in 715ms
flutter: ✅ Firebase initialization completed successfully
flutter: ✅ Preloaded asset: assets/images/onboarding_1.png (0 bytes)
flutter: ✅ Preloaded asset: assets/images/onboarding_2.png (0 bytes)
flutter: ✅ Preloaded asset: assets/images/onboarding_3.png (0 bytes)
flutter: ✅ Non-critical assets preloaded in 104ms
flutter: ✅ Firebase full features initialized in 422ms
flutter: ℹ️ INFO [2025-07-01T06:58:45.035916] [LoggingService] Logging service initialized successfully
flutter: ℹ️ INFO [2025-07-01T06:58:45.041965] [LoggingService] Device info: {name: iPhone 15 Pro Max, model: iPhone, systemName: iOS, systemVersion: 17.2, platform: ios}
flutter: ℹ️ INFO [2025-07-01T06:58:45.042993] [LoggingService] App info: Culture Connect 1.0.0+1
flutter: ℹ️ INFO [2025-07-01T06:58:45.058625] [ErrorHandlingService] Error handling service initialized
flutter: ℹ️ INFO [2025-07-01T06:58:45.077328] [CrashReportingService] Crash reporting service initialized
flutter: ℹ️ INFO [2025-07-01T06:58:45.092426] [AnalyticsService] Analytics service initialized
flutter: 🐛 DEBUG [2025-07-01T06:58:45.099007] [AnalyticsService] Event: app_session_begin {"category":"engagement","parameters":{"timestamp":"2025-07-01T06:58:45.085727"}}
flutter: ℹ️ INFO [2025-07-01T06:58:45.104173] [PerformanceMonitoringService] Performance monitoring service initialized
flutter: ℹ️ INFO [2025-07-01T06:58:45.104675] [App] All services initialized successfully
flutter: ⚠️ WARNING [2025-07-01T06:58:45.113849] [PerformanceMonitoringService] High memory usage detected {"memory_mb":153.0}
flutter: 🐛 DEBUG [2025-07-01T06:58:47.287968] [PerformanceMonitoringService] Slow frame detected {"duration_ms":211}
flutter: 🐛 DEBUG [2025-07-01T06:58:47.321600] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T06:58:52.759279] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-07-01T06:58:52.788553] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
Syncing files to device iPhone 15 Pro Max...                        3.6s

Flutter run key commands.
r Hot reload. 🔥🔥🔥
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).

A Dart VM Service on iPhone 15 Pro Max is available at: http://127.0.0.1:57384/W6qZtUawWM4=/
The Flutter DevTools debugger and profiler on iPhone 15 Pro Max is available at:
http://127.0.0.1:9101?uri=http://127.0.0.1:57384/W6qZtUawWM4=/
flutter: 🐛 DEBUG [2025-07-01T06:58:56.234449] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-07-01T06:58:58.559389] [PerformanceMonitoringService] Slow frame detected {"duration_ms":2311}
flutter: 🐛 DEBUG [2025-07-01T06:59:16.672111] [PerformanceMonitoringService] Slow frame detected {"duration_ms":18125}
flutter: 🐛 DEBUG [2025-07-01T06:59:16.745178] [PerformanceMonitoringService] Slow frame detected {"duration_ms":57}
flutter: 🐛 DEBUG [2025-07-01T06:59:16.772130] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-01T06:59:16.871237] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T06:59:20.977889] [PerformanceMonitoringService] Slow frame detected {"duration_ms":55}
flutter: 🐛 DEBUG [2025-07-01T06:59:21.029071] [PerformanceMonitoringService] Slow frame detected {"duration_ms":52}
flutter: 🐛 DEBUG [2025-07-01T06:59:21.055410] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-07-01T06:59:24.551863] [PerformanceMonitoringService] Slow frame detected {"duration_ms":213}
flutter: 🐛 DEBUG [2025-07-01T06:59:24.655820] [PerformanceMonitoringService] Slow frame detected {"duration_ms":103}
flutter: 🐛 DEBUG [2025-07-01T06:59:24.688051] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T06:59:24.788011] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-07-01T06:59:24.888231] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-01T06:59:24.921544] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T06:59:25.005716] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T06:59:25.055653] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T06:59:27.504858] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-07-01T06:59:27.604952] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-01T06:59:27.638809] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T06:59:27.671381] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T06:59:27.705674] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T06:59:27.954541] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: ⚠️ WARNING [2025-07-01T06:59:30.104452] [PerformanceMonitoringService] High memory usage detected {"memory_mb":154.0}
flutter: 🐛 DEBUG [2025-07-01T06:59:31.939127] [PerformanceMonitoringService] Slow frame detected {"duration_ms":1516}
flutter: 🐛 DEBUG [2025-07-01T06:59:32.288478] [PerformanceMonitoringService] Slow frame detected {"duration_ms":350}
flutter: 🐛 DEBUG [2025-07-01T06:59:33.038949] [PerformanceMonitoringService] Slow frame detected {"duration_ms":733}
flutter: 🐛 DEBUG [2025-07-01T06:59:34.188146] [PerformanceMonitoringService] Slow frame detected {"duration_ms":233}
flutter: 🐛 DEBUG [2025-07-01T06:59:34.721444] [PerformanceMonitoringService] Slow frame detected {"duration_ms":283}
flutter: 🐛 DEBUG [2025-07-01T06:59:35.921605] [PerformanceMonitoringService] Slow frame detected {"duration_ms":333}
flutter: 🐛 DEBUG [2025-07-01T06:59:38.989138] [PerformanceMonitoringService] Slow frame detected {"duration_ms":400}
flutter: 🐛 DEBUG [2025-07-01T06:59:41.872316] [PerformanceMonitoringService] Slow frame detected {"duration_ms":2883}
flutter: 🐛 DEBUG [2025-07-01T06:59:45.038199] [PerformanceMonitoringService] Slow frame detected {"duration_ms":1033}
flutter: ⚠️ WARNING [2025-07-01T06:59:45.105967] [PerformanceMonitoringService] High memory usage detected {"memory_mb":155.0}
flutter: 🐛 DEBUG [2025-07-01T06:59:48.071549] [PerformanceMonitoringService] Slow frame detected {"duration_ms":299}
flutter: 🐛 DEBUG [2025-07-01T06:59:50.372542] [PerformanceMonitoringService] Slow frame detected {"duration_ms":616}
flutter: 🐛 DEBUG [2025-07-01T06:59:51.021816] [PerformanceMonitoringService] Slow frame detected {"duration_ms":650}
flutter: ⚠️ WARNING [2025-07-01T07:00:00.114185] [PerformanceMonitoringService] High memory usage detected {"memory_mb":163.0}
flutter: 🐛 DEBUG [2025-07-01T07:00:00.189217] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:00:02.872718] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:00:05.476388] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-07-01T07:00:05.622739] [PerformanceMonitoringService] Slow frame detected {"duration_ms":145}
flutter: 🐛 DEBUG [2025-07-01T07:00:05.756048] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-07-01T07:00:05.789406] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:05.855151] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:00:05.905050] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:06.021761] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:06.571994] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-01T07:00:09.071976] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:09.222788] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:10.139467] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:10.356790] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:13.094004] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:00:15.113052] [PerformanceMonitoringService] High memory usage detected {"memory_mb":162.0}
flutter: 🐛 DEBUG [2025-07-01T07:00:19.413479] [PerformanceMonitoringService] Slow frame detected {"duration_ms":78}
flutter: 🐛 DEBUG [2025-07-01T07:00:19.434499] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-07-01T07:00:19.609684] [PerformanceMonitoringService] Slow frame detected {"duration_ms":65}
flutter: 🐛 DEBUG [2025-07-01T07:00:19.752032] [PerformanceMonitoringService] Slow frame detected {"duration_ms":150}
flutter: 🐛 DEBUG [2025-07-01T07:00:19.791113] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: 🐛 DEBUG [2025-07-01T07:00:19.901580] [PerformanceMonitoringService] Slow frame detected {"duration_ms":110}
flutter: 🐛 DEBUG [2025-07-01T07:00:20.052254] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:00:23.555562] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:00:25.505910] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:26.623209] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:27.958313] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:00:29.292051] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:00:29.474559] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:29.909270] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: ⚠️ WARNING [2025-07-01T07:00:30.125507] [PerformanceMonitoringService] High memory usage detected {"memory_mb":175.0}
flutter: 🐛 DEBUG [2025-07-01T07:00:30.209427] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:00:30.676168] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:31.710901] [PerformanceMonitoringService] Slow frame detected {"duration_ms":52}
flutter: 🐛 DEBUG [2025-07-01T07:00:32.009270] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-01T07:00:32.526068] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:00:32.909974] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:00:33.242902] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:33.493722] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:00:33.776528] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:00:34.293755] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:00:34.596017] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-07-01T07:00:34.826956] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-01T07:00:34.884395] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-07-01T07:00:34.911024] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-07-01T07:00:34.993715] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:35.050688] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-07-01T07:00:35.077910] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-01T07:00:35.911567] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:00:38.077969] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:39.428473] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:00:39.812579] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-01T07:00:42.397807] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:00:42.462308] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:00:42.495868] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:42.546001] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:42.579448] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:42.613282] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:42.845850] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:00:45.135633] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 🐛 DEBUG [2025-07-01T07:00:46.230066] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:00:46.314045] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:46.347324] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:46.398123] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:00:47.063406] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:47.297440] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-07-01T07:00:47.950559] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-07-01T07:00:48.231112] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:00:48.396853] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:00:49.180711] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:00:49.465195] [PerformanceMonitoringService] Slow frame detected {"duration_ms":35}
flutter: 🐛 DEBUG [2025-07-01T07:00:49.664691] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:00:50.981416] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:00:52.380844] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:00:53.847699] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:00:53.915149] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:00:55.015130] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-01T07:00:57.917785] [PerformanceMonitoringService] Slow frame detected {"duration_ms":102}
flutter: 🐛 DEBUG [2025-07-01T07:00:58.065693] [PerformanceMonitoringService] Slow frame detected {"duration_ms":147}
flutter: 🐛 DEBUG [2025-07-01T07:00:58.231344] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: Error getting user model: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: 🐛 DEBUG [2025-07-01T07:00:59.231462] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: Unexpected error during login: type 'List<Object?>' is not a subtype of type 'PigeonUserDetails?' in type cast
flutter: 🐛 DEBUG [2025-07-01T07:00:59.669079] [ExperiencesNotifier] Loading experiences
flutter: ❌ ERROR [2025-07-01T07:01:01.752172] [FlutterError] 'package:flutter_riverpod/src/consumer.dart': Failed assertion: line 600 pos 7: 'debugDoingBuild': ref.listen can only be used within the build method of a ConsumerWidget 'package:flutter_riverpod/src/consumer.dart': Failed assertion: line 600 pos 7: 'debugDoingBuild': ref.listen can only be used within the build method of a ConsumerWidget
flutter: Stack trace:
flutter: #0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:50:61)
flutter: #1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:40:5)
flutter: #2      ConsumerStatefulElement.listen (package:flutter_riverpod/src/consumer.dart:600:7)
flutter: #3      AutoLockService.initialize (package:culture_connect/services/auto_lock_service.dart:31:9)
flutter: #4      _MainNavigationState.initState.<anonymous closure> (package:culture_connect/screens/main_navigation.dart:42:15)
flutter: #5      SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #6      SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1331:11)
flutter: #7      SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #8      _invoke (dart:ui/hooks.dart:312:13)
flutter: #9      PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #10     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-01T07:01:01.814514] [ExperiencesNotifier] Refreshing experiences
flutter: 🐛 DEBUG [2025-07-01T07:01:01.815334] [ExperiencesNotifier] Loading experiences
flutter: 🐛 DEBUG [2025-07-01T07:01:01.847362] [ExperiencesNotifier] Loaded 6 experiences
flutter: Error getting user model: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: 🐛 DEBUG [2025-07-01T07:01:01.872571] [PerformanceMonitoringService] Slow frame detected {"duration_ms":2341}
flutter: ❌ ERROR [2025-07-01T07:01:02.228215] [FlutterError] dependOnInheritedWidgetOfExactType<_InheritedTheme>() or dependOnInheritedElement() was called before _AnimatedRefreshIndicatorState.initState() completed.
flutter: When an inherited widget changes, for example if the value of Theme.of() changes, its dependent widgets are rebuilt. If the dependent widget's reference to the inherited widget is in a constructor or an initState() method, then the rebuilt dependent widget will not reflect the changes in the inherited widget.
flutter: Typically references to inherited widgets should occur in widget build() methods. Alternatively, initialization based on inherited widgets can be placed in the didChangeDependencies method, which is called after initState and whenever the dependencies change thereafter. dependOnInheritedWidgetOfExactType<_InheritedTheme>() or dependOnInheritedElement() was called before _AnimatedRefreshIndicatorState.initState() completed.
flutter: When an inherited widget changes, for example if the value of Theme.of() changes, its dependent widgets are rebuilt. If the dependent widget's reference to the inherited widget is in a constructor or an initState() method, then the rebuilt dependent widget will not reflect the changes in the inherited widget.
flutter: Typically references to inherited widgets should occur in widget build() methods. Alternatively, initialization based on inherited widgets can be placed in the didChangeDependencies method, which is called after initState and whenever the dependencies change thereafter.
flutter: Stack trace:
flutter: #0      StatefulElement.dependOnInheritedElement.<anonymous closure> (package:flutter/src/widgets/framework.dart:5864:9)
flutter: #1      StatefulElement.dependOnInheritedElement (package:flutter/src/widgets/framework.dart:5907:6)
flutter: #2      Element.dependOnInheritedWidgetOfExactType (package:flutter/src/widgets/framework.dart:4922:14)
flutter: #3      Theme.of (package:flutter/src/material/theme.dart:119:53)
flutter: #4      _AnimatedRefreshIndicatorState._initializeAnimations (package:culture_connect/widgets/common/animated_refresh_indicator.dart:159:25)
flutter: #5      _AnimatedRefreshIndicatorState.initState (package:culture_connect/widgets/common/animated_refresh_indicator.dart:69:5)
flutter: #6      StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5762:55)
flutter: #7      ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #8      Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #9      Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #10     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #11     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #12     Element.updateChild (package:flutter/src/widgets/framework.dart:3957:20)
flutter: #13     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #14     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #15     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #16     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #17     SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #18     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #19     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #20     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #21     StatelessElement.update (package:flutter/src/widgets/framework.dart:5707:5)
flutter: #22     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #23     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #24     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #25     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #26     BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #27     BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #28     BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #29     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1176:21)
flutter: #30     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #31     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #32     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #33     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #34     _invoke (dart:ui/hooks.dart:312:13)
flutter: #35     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #36     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-01T07:01:04.250013] [ExperiencesNotifier] Loaded 6 experiences
flutter: 🐛 DEBUG [2025-07-01T07:01:04.265177] [PerformanceMonitoringService] Slow frame detected {"duration_ms":2392}
flutter: 🐛 DEBUG [2025-07-01T07:01:04.366687] [PerformanceMonitoringService] Slow frame detected {"duration_ms":101}
flutter: 🐛 DEBUG [2025-07-01T07:01:04.531927] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:01:04.983383] [PerformanceMonitoringService] Slow frame detected {"duration_ms":35}
flutter: 🐛 DEBUG [2025-07-01T07:01:05.154763] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-07-01T07:01:05.182826] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-01T07:01:06.045759] [PerformanceMonitoringService] Slow frame detected {"duration_ms":97}
flutter: 🐛 DEBUG [2025-07-01T07:01:06.066023] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-01T07:01:06.234945] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-07-01T07:01:07.067146] [PerformanceMonitoringService] Slow frame detected {"duration_ms":68}
flutter: 🐛 DEBUG [2025-07-01T07:01:07.590973] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-07-01T07:01:07.623038] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: ⚠️ WARNING [2025-07-01T07:01:15.136164] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: 🐛 DEBUG [2025-07-01T07:01:22.087446] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-07-01T07:01:22.115941] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-01T07:01:28.299814] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: ⚠️ WARNING [2025-07-01T07:01:30.135489] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: ⚠️ WARNING [2025-07-01T07:01:40.182854] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: 🐛 DEBUG [2025-07-01T07:01:41.233687] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:01:41.816375] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:01:43.015167] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:01:43.033369] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:01:45.134308] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 🐛 DEBUG [2025-07-01T07:01:58.416820] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:01:58.450528] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:02:00.134518] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: ⚠️ WARNING [2025-07-01T07:02:15.134316] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: ⚠️ WARNING [2025-07-01T07:02:30.135090] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 🐛 DEBUG [2025-07-01T07:02:33.841023] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-07-01T07:02:33.867068] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-07-01T07:02:34.666971] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:02:38.918133] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:02:39.400382] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-07-01T07:02:43.119774] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:02:45.133238] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 🐛 DEBUG [2025-07-01T07:02:47.284080] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:02:50.317194] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:02:57.718158] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:02:58.351596] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:02:58.385394] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:02:59.801456] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:02:59.934916] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:03:00.136215] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: 🐛 DEBUG [2025-07-01T07:03:00.418846] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:03:00.517677] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:03:00.734982] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:03:00.851473] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:03:01.133997] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:03:01.500743] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:03:01.967499] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:03:15.134248] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 🐛 DEBUG [2025-07-01T07:03:20.618058] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:03:30.133976] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: ⚠️ WARNING [2025-07-01T07:03:45.134294] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 🐛 DEBUG [2025-07-01T07:03:50.268016] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:03:55.335148] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:03:55.368208] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:03:55.401482] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:04:00.135193] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 🐛 DEBUG [2025-07-01T07:04:06.635130] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:04:12.652318] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:04:14.852576] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:04:14.902562] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: ⚠️ WARNING [2025-07-01T07:04:15.134464] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 🐛 DEBUG [2025-07-01T07:04:25.935754] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:04:27.487618] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:04:27.538075] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:04:30.134888] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 🐛 DEBUG [2025-07-01T07:04:36.185796] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:04:37.872309] [PerformanceMonitoringService] Slow frame detected {"duration_ms":53}
flutter: 🐛 DEBUG [2025-07-01T07:04:38.557778] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-07-01T07:04:38.586168] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-01T07:04:38.902609] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:04:40.380393] [PerformanceMonitoringService] Slow frame detected {"duration_ms":61}
flutter: 🐛 DEBUG [2025-07-01T07:04:40.401907] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-07-01T07:04:40.495095] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-07-01T07:04:40.519561] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23}
flutter: 🐛 DEBUG [2025-07-01T07:04:41.118782] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:04:43.002556] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:04:43.608138] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-07-01T07:04:43.635620] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-01T07:04:44.402364] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:04:45.134476] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 🐛 DEBUG [2025-07-01T07:04:48.918659] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-01T07:04:49.718855] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:04:51.115143] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-07-01T07:04:51.136333] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: ⚠️ WARNING [2025-07-01T07:05:00.135498] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 🐛 DEBUG [2025-07-01T07:05:08.803113] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:05:09.089456] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-07-01T07:05:09.308226] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: 🐛 DEBUG [2025-07-01T07:05:09.336469] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-01T07:05:10.052729] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:05:10.519741] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:05:11.352793] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:05:11.419086] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:05:12.755108] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-07-01T07:05:12.819641] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:05:13.104495] [PerformanceMonitoringService] Slow frame detected {"duration_ms":101}
flutter: 🐛 DEBUG [2025-07-01T07:05:13.302546] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:05:13.486674] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-07-01T07:05:13.610524] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-07-01T07:05:13.635639] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-07-01T07:05:13.735625] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:05:13.854890] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:05:13.939848] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-01T07:05:14.035815] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:05:14.236572] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:05:14.600884] [PerformanceMonitoringService] Slow frame detected {"duration_ms":132}
flutter: 🐛 DEBUG [2025-07-01T07:05:14.620229] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: 🐛 DEBUG [2025-07-01T07:05:14.686006] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:05:17.019303] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:05:45.136224] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 🐛 DEBUG [2025-07-01T07:05:50.703070] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:05:51.367212] [PerformanceMonitoringService] Slow frame detected {"duration_ms":279}
flutter: 🐛 DEBUG [2025-07-01T07:05:51.496014] [PerformanceMonitoringService] Slow frame detected {"duration_ms":130}
flutter: 🐛 DEBUG [2025-07-01T07:05:51.528362] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23}
flutter: 🐛 DEBUG [2025-07-01T07:05:51.579545] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-07-01T07:05:51.627529] [PerformanceMonitoringService] Slow frame detected {"duration_ms":44}
flutter: 🐛 DEBUG [2025-07-01T07:05:51.669913] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:05:51.726628] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-07-01T07:05:51.752932] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-01T07:05:52.719511] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:05:52.836397] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:05:52.953803] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:06:00.135703] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 🐛 DEBUG [2025-07-01T07:06:02.819704] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:06:05.203132] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-01T07:06:06.069828] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:06:15.136335] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: 🐛 DEBUG [2025-07-01T07:06:18.047971] [PerformanceMonitoringService] Slow frame detected {"duration_ms":44}
flutter: 🐛 DEBUG [2025-07-01T07:06:18.070786] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-07-01T07:06:18.619858] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:06:30.136325] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: 🐛 DEBUG [2025-07-01T07:06:30.320477] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:06:34.853902] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:06:40.353682] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-01T07:06:45.141198] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: ⚠️ WARNING [2025-07-01T07:07:00.137379] [PerformanceMonitoringService] High memory usage detected {"memory_mb":187.0}
flutter: ⚠️ WARNING [2025-07-01T07:07:15.139481] [PerformanceMonitoringService] High memory usage detected {"memory_mb":189.0}
flutter: ⚠️ WARNING [2025-07-01T07:07:30.138226] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: ⚠️ WARNING [2025-07-01T07:07:40.151686] [PerformanceMonitoringService] High memory usage detected {"memory_mb":151.0}
flutter: 🐛 DEBUG [2025-07-01T07:07:48.661218] [PerformanceMonitoringService] Slow frame detected {"duration_ms":238}
flutter: 🐛 DEBUG [2025-07-01T07:07:48.687909] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-01T07:07:54.873627] [PerformanceMonitoringService] Slow frame detected {"duration_ms":419}
flutter: 🐛 DEBUG [2025-07-01T07:07:54.971584] [PerformanceMonitoringService] Slow frame detected {"duration_ms":97}
flutter: 🐛 DEBUG [2025-07-01T07:07:55.317832] [PerformanceMonitoringService] Slow frame detected {"duration_ms":346}
flutter: 🐛 DEBUG [2025-07-01T07:07:55.454584] [PerformanceMonitoringService] Slow frame detected {"duration_ms":136}
flutter: 🐛 DEBUG [2025-07-01T07:07:55.508949] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-07-01T07:07:55.562930] [PerformanceMonitoringService] Slow frame detected {"duration_ms":53}
flutter: 🐛 DEBUG [2025-07-01T07:07:55.609737] [PerformanceMonitoringService] Slow frame detected {"duration_ms":47}
flutter: 🐛 DEBUG [2025-07-01T07:07:55.649717] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-01T07:07:55.683951] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-07-01T07:07:55.729933] [PerformanceMonitoringService] Slow frame detected {"duration_ms":45}
flutter: 🐛 DEBUG [2025-07-01T07:07:55.754483] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-01T07:07:56.754845] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:07:57.654534] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-07-01T07:07:57.722033] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:07:57.822030] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-01T07:07:57.888832] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:07:57.963707] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-07-01T07:07:57.987976] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-01T07:07:58.071790] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: ⚠️ WARNING [2025-07-01T07:08:00.137123] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: 🐛 DEBUG [2025-07-01T07:08:01.854570] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:08:01.904716] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-01T07:08:08.176677] [PerformanceMonitoringService] Slow frame detected {"duration_ms":55}
flutter: 🐛 DEBUG [2025-07-01T07:08:08.205739] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-01T07:08:09.661009] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:08:09.738299] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-01T07:08:09.884739] [PerformanceMonitoringService] Slow frame detected {"duration_ms":146}
flutter: 🐛 DEBUG [2025-07-01T07:08:09.904669] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-01T07:08:11.671444] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-07-01T07:08:13.638093] [PerformanceMonitoringService] Slow frame detected {"duration_ms":1966}
flutter: 🐛 DEBUG [2025-07-01T07:08:14.060868] [PerformanceMonitoringService] Slow frame detected {"duration_ms":422}
flutter: 🐛 DEBUG [2025-07-01T07:08:14.420129] [PerformanceMonitoringService] Slow frame detected {"duration_ms":356}
flutter: 🐛 DEBUG [2025-07-01T07:08:14.438233] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-07-01T07:08:14.471560] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:08:14.901193] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-07-01T07:08:14.922554] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: ⚠️ WARNING [2025-07-01T07:08:15.147950] [PerformanceMonitoringService] High memory usage detected {"memory_mb":197.0}
flutter: 🐛 DEBUG [2025-07-01T07:08:15.457129] [PerformanceMonitoringService] Slow frame detected {"duration_ms":69}
flutter: 🐛 DEBUG [2025-07-01T07:08:19.037350] [PerformanceMonitoringService] Slow frame detected {"duration_ms":65}
flutter: 🐛 DEBUG [2025-07-01T07:08:19.121891] [PerformanceMonitoringService] Slow frame detected {"duration_ms":84}
flutter: 🐛 DEBUG [2025-07-01T07:08:19.171503] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T07:08:19.305488] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:08:23.805781] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-01T07:08:23.838426] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:08:23.871595] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:08:23.904949] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:08:23.938300] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:08:23.971766] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T07:08:24.804320] [PerformanceMonitoringService] Slow frame detected {"duration_ms":65}
flutter: 🐛 DEBUG [2025-07-01T07:08:24.821549] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: ⚠️ WARNING [2025-07-01T07:08:25.157835] [PerformanceMonitoringService] High memory usage detected {"memory_mb":153.0}
flutter: 🐛 DEBUG [2025-07-01T07:08:26.088307] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: ❌ ERROR [2025-07-01T07:08:26.745178] [FlutterError] A RenderFlex overflowed by 15 pixels on the bottom. A RenderFlex overflowed by 15 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #19     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #20     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #21     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #22     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #23     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #24     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #25     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderCustomPaint.paint (package:flutter/src/rendering/custom_paint.dart:636:11)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderPhysicalShape.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2196:15)
flutter: #34     PaintingContext.pushClipPath.<anonymous closure> (package:flutter/src/rendering/object.dart:638:81)
flutter: #35     ClipContext._clipAndPaint (package:flutter/src/painting/clip.dart:28:12)
flutter: #36     ClipContext.clipPathAndPaint (package:flutter/src/painting/clip.dart:40:5)
flutter: #37     PaintingContext.pushClipPath (package:flutter/src/rendering/object.dart:638:7)
flutter: #38     RenderPhysicalShape.paint (package:flutter/src/rendering/proxy_box.dart:2183:21)
flutter: #39     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #40     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #41     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #42     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #43     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #44     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #45     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #46     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #47     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #48     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #49     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #50     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #51     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #52     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #53     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #54     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #55     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #56     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #57     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #58     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #59     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #63     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #64     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #65     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #66     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #67     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #68     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #69     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #70     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #71     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #72     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #73     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #74     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #75     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #77     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #78     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #80     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #85     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #86     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #87     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #88     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #89     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #90     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #91     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #92     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #93     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #94     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #95     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #96     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #97     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #98     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #99     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #103    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #104    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #105    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #106    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #107    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #108    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #109    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #111    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #112    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #113    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #114    RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #115    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #116    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #117    RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #118    PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #119    PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #120    RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #123    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #124    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #125    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #126    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #127    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #128    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #129    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #130    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #131    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #132    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #133    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #134    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #135    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #136    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #137    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #138    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #142    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #143    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #144    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #145    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #146    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #147    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #148    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #149    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #150    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #151    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #152    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #153    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #154    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #155    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #156    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #157    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #158    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #159    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #160    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #161    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #162    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #163    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #164    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #165    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #166    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #167    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #168    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #169    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #170    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #171    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #172    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #173    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #174    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #175    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #176    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #177    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #178    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #179    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #180    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #181    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #182    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #183    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #184    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #185    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #186    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #187    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #188    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #189    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #190    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #191    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #192    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #193    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #194    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #195    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #196    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #197    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #198    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #199    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #200    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #201    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #202    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #203    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #204    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #205    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #206    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #207    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #208    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #209    _invoke (dart:ui/hooks.dart:312:13)
flutter: #210    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #211    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-01T07:08:26.893004] [FlutterError] A RenderFlex overflowed by 15 pixels on the bottom. A RenderFlex overflowed by 15 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #19     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #20     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #21     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #22     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #23     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #24     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #25     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderCustomPaint.paint (package:flutter/src/rendering/custom_paint.dart:636:11)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderPhysicalShape.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2196:15)
flutter: #34     PaintingContext.pushClipPath.<anonymous closure> (package:flutter/src/rendering/object.dart:638:81)
flutter: #35     ClipContext._clipAndPaint (package:flutter/src/painting/clip.dart:28:12)
flutter: #36     ClipContext.clipPathAndPaint (package:flutter/src/painting/clip.dart:40:5)
flutter: #37     PaintingContext.pushClipPath (package:flutter/src/rendering/object.dart:638:7)
flutter: #38     RenderPhysicalShape.paint (package:flutter/src/rendering/proxy_box.dart:2183:21)
flutter: #39     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #40     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #41     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #42     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #43     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #44     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #45     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #46     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #47     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #48     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #49     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #50     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #51     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #52     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #53     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #54     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #55     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #56     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #57     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #58     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #59     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #63     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #64     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #65     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #66     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #67     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #68     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #69     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #70     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #71     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #72     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #73     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #74     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #75     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #77     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #78     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #80     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #85     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #86     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #87     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #88     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #89     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #90     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #91     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #92     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #93     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #94     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #95     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #96     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #97     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #98     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #99     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #103    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #104    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #105    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #106    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #107    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #108    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #109    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #111    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #112    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #113    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #114    RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #115    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #116    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #117    RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #118    PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #119    PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #120    RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #123    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #124    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #125    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #126    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #127    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #128    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #129    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #130    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #131    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #132    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #133    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #134    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #135    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #136    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #137    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #138    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #142    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #143    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #144    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #145    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #146    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #147    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #148    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #149    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #150    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #151    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #152    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #153    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #154    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #155    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #156    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #157    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #158    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #159    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #160    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #161    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #162    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #163    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #164    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #165    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #166    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #167    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #168    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #169    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #170    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #171    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #172    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #173    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #174    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #175    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #176    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #177    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #178    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #179    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #180    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #181    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #182    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #183    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #184    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
