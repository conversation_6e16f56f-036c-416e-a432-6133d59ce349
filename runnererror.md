-------------------------------------
Translated Report (Full Report Below)
-------------------------------------

Incident Identifier: 691612FD-AABF-42F9-B7A9-CA9E2A7F81CF
CrashReporter Key:   18C6C8E6-F6CA-43EA-C8A5-26AEBFD4BB5E
Hardware Model:      MacBookPro14,3
Process:             Runner [33720]
Path:                /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Runner
Identifier:          com.example.cultureConnect
Version:             1.0.0 (1)
Code Type:           X86-64 (Native)
Role:                Foreground
Parent Process:      launchd_sim [7748]
Coalition:           com.apple.CoreSimulator.SimDevice.9140C1FA-E7C6-41E3-8897-74037AD695D1 [6601]
Responsible Process: SimulatorTrampoline [4183]

Date/Time:           2025-07-02 15:33:06.4563 +0100
Launch Time:         2025-07-02 15:30:22.3083 +0100
OS Version:          macOS 13.7.6 (22H625)
Release Type:        User
Report Version:      104

Exception Type:  EXC_CRASH (SIGABRT)
Exception Codes: 0x0000000000000000, 0x0000000000000000
Termination Reason: SIGNAL 6 Abort trap: 6
Terminating Process: Runner [33720]

Triggered by Thread:  0

Last Exception Backtrace:
0   CoreFoundation                	    0x7ff80049b751 __exceptionPreprocess + 226
1   libobjc.A.dylib               	    0x7ff800063904 objc_exception_throw + 48
2   CoreFoundation                	    0x7ff80049b63f -[NSException initWithCoder:] + 0
3   Runner                        	       0x10744cb37 +[GMSServices checkServicePreconditions] + 245
4   Runner                        	       0x10744a96e +[GMSServices preLaunchServices] + 106
5   Runner                        	       0x10984e103 -[FLTGoogleMapFactory sharedMapServices] + 51
6   Runner                        	       0x10984e017 -[FLTGoogleMapFactory createWithFrame:viewIdentifier:arguments:] + 87
7   Flutter                       	       0x11e359973 flutter::PlatformViewsController::OnCreate(FlutterMethodCall*, void (objc_object*) block_pointer) + 851
8   Flutter                       	       0x11e3594c1 flutter::PlatformViewsController::OnMethodCall(FlutterMethodCall*, void (objc_object*) block_pointer) + 131
9   Flutter                       	       0x11e9a215d __45-[FlutterMethodChannel setMethodCallHandler:]_block_invoke + 168
10  Flutter                       	       0x11e36905e invocation function for block in flutter::PlatformMessageHandlerIos::HandlePlatformMessage(std::_fl::unique_ptr<flutter::PlatformMessage, std::_fl::default_delete<flutter::PlatformMessage>>) + 94
11  libdispatch.dylib             	    0x7ff800156a90 _dispatch_call_block_and_release + 12
12  libdispatch.dylib             	    0x7ff800157d3a _dispatch_client_callout + 8
13  libdispatch.dylib             	    0x7ff800166ac0 _dispatch_main_queue_drain + 1420
14  libdispatch.dylib             	    0x7ff800166526 _dispatch_main_queue_callback_4CF + 31
15  CoreFoundation                	    0x7ff8003f7dc4 __CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__ + 9
16  CoreFoundation                	    0x7ff8003f26ff __CFRunLoopRun + 2463
17  CoreFoundation                	    0x7ff8003f197d CFRunLoopRunSpecific + 557
18  GraphicsServices              	    0x7ff80fe9d08f GSEventRunModal + 137
19  UIKitCore                     	    0x7ff805bbb53d -[UIApplication _run] + 972
20  UIKitCore                     	    0x7ff805bbffab UIApplicationMain + 123
21  UIKitCore                     	    0x7ff804c50ea3 0x7ff804bbe000 + 601763
22  Runner                        	       0x107352b7b static UIApplicationDelegate.main() + 123
23  Runner                        	       0x107352af7 static AppDelegate.$main() + 39
24  Runner                        	       0x107352be8 main + 24 (AppDelegate.swift:5)
25  dyld_sim                      	       0x10c20c3e0 start_sim + 10
26  dyld                          	       0x11b0fb418 start + 1896

Kernel Triage:
VM - (arg = 0x0) pmap_enter retried due to resource shortage
VM - (arg = 0x0) pmap_enter retried due to resource shortage
VM - (arg = 0x0) pmap_enter retried due to resource shortage
VM - (arg = 0x0) pmap_enter retried due to resource shortage
VM - (arg = 0x0) pmap_enter retried due to resource shortage


Thread 0 Crashed::  Dispatch queue: com.apple.main-thread
0   libsystem_kernel.dylib        	       0x110e86196 __pthread_kill + 10
1   libsystem_pthread.dylib       	       0x10da7dee6 pthread_kill + 263
2   libsystem_c.dylib             	    0x7ff80014cdd0 __abort + 145
3   libsystem_c.dylib             	    0x7ff80014cd3f abort + 148
4   libc++abi.dylib               	    0x7ff8002961b2 abort_message + 241
5   libc++abi.dylib               	    0x7ff80028838a demangling_terminate_handler() + 266
6   libobjc.A.dylib               	    0x7ff800042666 _objc_terminate() + 96
7   FirebaseCrashlytics           	       0x10cd63885 FIRCLSTerminateHandler() + 325 (FIRCLSException.mm:463)
8   libc++abi.dylib               	    0x7ff80029560b std::__terminate(void (*)()) + 6
9   libc++abi.dylib               	    0x7ff8002955c6 std::terminate() + 54
10  libdispatch.dylib             	    0x7ff800157d4e _dispatch_client_callout + 28
11  libdispatch.dylib             	    0x7ff800166ac0 _dispatch_main_queue_drain + 1420
12  libdispatch.dylib             	    0x7ff800166526 _dispatch_main_queue_callback_4CF + 31
13  CoreFoundation                	    0x7ff8003f7dc4 __CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__ + 9
14  CoreFoundation                	    0x7ff8003f26ff __CFRunLoopRun + 2463
15  CoreFoundation                	    0x7ff8003f197d CFRunLoopRunSpecific + 557
16  GraphicsServices              	    0x7ff80fe9d08f GSEventRunModal + 137
17  UIKitCore                     	    0x7ff805bbb53d -[UIApplication _run] + 972
18  UIKitCore                     	    0x7ff805bbffab UIApplicationMain + 123
19  UIKitCore                     	    0x7ff804c50ea3 0x7ff804bbe000 + 601763
20  Runner                        	       0x107352b7b static UIApplicationDelegate.main() + 123
21  Runner                        	       0x107352af7 static AppDelegate.$main() + 39
22  Runner                        	       0x107352be8 main + 24 (AppDelegate.swift:5)
23  dyld_sim                      	       0x10c20c3e0 start_sim + 10
24  dyld                          	       0x11b0fb418 start + 1896

Thread 1::  Dispatch queue: com.apple.root.background-qos
0   connectivity_plus             	       0x10d2f57b8 DYLD-STUB$$NWPath.usesInterfaceType(_:) + 0
1   connectivity_plus             	       0x10d2efae7 PathMonitorConnectivityProvider.currentConnectivityType.getter + 455 (PathMonitorConnectivityProvider.swift:15)
2   connectivity_plus             	       0x10d2f0480 PathMonitorConnectivityProvider.pathUpdateHandler(path:) + 160 (PathMonitorConnectivityProvider.swift:58)
3   connectivity_plus             	       0x10d2f03ca implicit closure #2 in implicit closure #1 in PathMonitorConnectivityProvider.ensurePathMonitor() + 42
4   Network                       	    0x7ff8079bb32c partial apply for closure #1 in NWPathMonitor.startLocked(lockedState:) + 44
5   Network                       	    0x7ff8079b3439 thunk for @escaping @callee_guaranteed @Sendable () -> () + 25
6   libdispatch.dylib             	    0x7ff800156a90 _dispatch_call_block_and_release + 12
7   libdispatch.dylib             	    0x7ff800157d3a _dispatch_client_callout + 8
8   libdispatch.dylib             	    0x7ff80016a782 _dispatch_root_queue_drain + 1240
9   libdispatch.dylib             	    0x7ff80016ae88 _dispatch_worker_thread2 + 244
10  libsystem_pthread.dylib       	       0x10da7ac0f _pthread_wqthread + 257
11  libsystem_pthread.dylib       	       0x10da79bbf start_wqthread + 15

Thread 2::  Dispatch queue: com.apple.root.background-qos
0   libswiftCore.dylib            	    0x7ff8150c9d4f swift_conformsToProtocolMaybeInstantiateSuperclasses(swift::TargetMetadata<swift::InProcess> const*, swift::TargetProtocolDescriptor<swift::InProcess> const*, bool) + 3583
1   libswiftCore.dylib            	    0x7ff8150c855e swift_conformsToProtocolCommon + 78
2   libswiftCore.dylib            	    0x7ff815078109 swift::_conformsToProtocol(swift::OpaqueValue const*, swift::TargetMetadata<swift::InProcess> const*, swift::TargetProtocolDescriptorRef<swift::InProcess>, swift::TargetWitnessTable<swift::InProcess> const**) + 41
3   libswiftCore.dylib            	    0x7ff8150c714d swift::_checkGenericRequirements(__swift::__runtime::llvm::ArrayRef<swift::TargetGenericRequirementDescriptor<swift::InProcess>>, __swift::__runtime::llvm::SmallVectorImpl<void const*>&, std::__1::function<void const* (unsigned int, unsigned int)>, std::__1::function<swift::TargetWitnessTable<swift::InProcess> const* (swift::TargetMetadata<swift::InProcess> const*, unsigned int)>) + 3981
4   libswiftCore.dylib            	    0x7ff8150b309e _gatherGenericParameters(swift::TargetContextDescriptor<swift::InProcess> const*, __swift::__runtime::llvm::ArrayRef<swift::MetadataOrPack>, swift::TargetMetadata<swift::InProcess> const*, __swift::__runtime::llvm::SmallVectorImpl<unsigned int>&, __swift::__runtime::llvm::SmallVectorImpl<void const*>&, swift::Demangle::__runtime::Demangler&) + 2750
5   libswiftCore.dylib            	    0x7ff8150bc247 (anonymous namespace)::DecodedMetadataBuilder::createBoundGenericType(swift::TargetContextDescriptor<swift::InProcess> const*, __swift::__runtime::llvm::ArrayRef<swift::MetadataOrPack>, swift::MetadataOrPack) const + 199
6   libswiftCore.dylib            	    0x7ff8150ba932 swift::Demangle::__runtime::TypeDecoder<(anonymous namespace)::DecodedMetadataBuilder>::decodeMangledType(swift::Demangle::__runtime::Node*, unsigned int, bool) + 13666
7   libswiftCore.dylib            	    0x7ff8150b4ea8 swift_getTypeByMangledNodeImpl(swift::MetadataRequest, swift::Demangle::__runtime::Demangler&, swift::Demangle::__runtime::Node*, void const* const*, std::__1::function<void const* (unsigned int, unsigned int)>, std::__1::function<swift::TargetWitnessTable<swift::InProcess> const* (swift::TargetMetadata<swift::InProcess> const*, unsigned int)>) + 568
8   libswiftCore.dylib            	    0x7ff8150b4c1b swift_getTypeByMangledNode + 491
9   libswiftCore.dylib            	    0x7ff8150b541c swift_getTypeByMangledNameImpl(swift::MetadataRequest, __swift::__runtime::llvm::StringRef, void const* const*, std::__1::function<void const* (unsigned int, unsigned int)>, std::__1::function<swift::TargetWitnessTable<swift::InProcess> const* (swift::TargetMetadata<swift::InProcess> const*, unsigned int)>) + 1036
10  libswiftCore.dylib            	    0x7ff8150b07bb swift_getTypeByMangledName + 491
11  libswiftCore.dylib            	    0x7ff8150b0ab7 swift_getTypeByMangledNameInContextImpl(char const*, unsigned long, swift::TargetContextDescriptor<swift::InProcess> const*, void const* const*) + 183
12  FirebaseSessions              	       0x10d043607 __swift_instantiateConcreteTypeFromMangledName + 87
13  FirebaseSessions              	       0x10d05f2d1 CacheKey.init(from:) + 113
14  FirebaseSessions              	       0x10d05f7df protocol witness for Decodable.init(from:) in conformance CacheKey + 15
15  libswiftCore.dylib            	    0x7ff8150467e7 dispatch thunk of Decodable.init(from:) + 7
16  Foundation                    	    0x7ff800c00e04 specialized JSONDecoderImpl.unwrap<A, B>(_:as:for:_:) + 996
17  Foundation                    	    0x7ff800c1bf23 partial apply for closure #1 in JSONDecoder.decode<A>(_:from:) + 67
18  Foundation                    	    0x7ff800c0266e closure #1 in JSONDecoder._decode<A>(_:from:) + 974
19  Foundation                    	    0x7ff800c31eb9 partial apply for closure #1 in JSONDecoder._decode<A>(_:from:) + 25
20  Foundation                    	    0x7ff800c02a2b closure #1 in static JSONDecoder.withUTF8Representation<A>(of:_:) + 827
21  Foundation                    	    0x7ff800c31ed9 partial apply for closure #1 in static JSONDecoder.withUTF8Representation<A>(of:_:) + 25
22  Foundation                    	    0x7ff800a784a6 closure #1 in Data.withBufferView<A>(_:) + 22
23  Foundation                    	    0x7ff800c31ef5 partial apply for closure #1 in Data.withBufferView<A>(_:) + 21
24  Foundation                    	    0x7ff800c382d5 Data.InlineSlice.withUnsafeBytes<A>(_:) + 69
25  Foundation                    	    0x7ff800c01b42 JSONDecoder._decode<A>(_:from:) + 642
26  Foundation                    	    0x7ff800c00a10 JSONDecoder.decode<A>(_:from:) + 48
27  Foundation                    	    0x7ff800c1c58f dispatch thunk of JSONDecoder.decode<A>(_:from:) + 15
28  FirebaseSessions              	       0x10d0602a9 SettingsCache.cacheKey.getter + 745 (SettingsCacheClient.swift:74)
29  FirebaseSessions              	       0x10d0610d5 protocol witness for SettingsCacheClient.cacheKey.getter in conformance SettingsCache + 21
30  FirebaseSessions              	       0x10d05144a RemoteSettings.isCacheExpired(time:) + 778 (RemoteSettings.swift:113)
31  FirebaseSessions              	       0x10d05100b RemoteSettings.fetchAndCacheSettings(currentTime:) + 107 (RemoteSettings.swift:56)
32  FirebaseSessions              	       0x10d052725 RemoteSettings.updateSettings(currentTime:) + 37 (RemoteSettings.swift:97)
33  FirebaseSessions              	       0x10d0522c0 RemoteSettings.updateSettings() + 80 (RemoteSettings.swift:101)
34  FirebaseSessions              	       0x10d056fa1 SessionsSettings.updateSettings() + 81 (SessionsSettings.swift:81)
35  FirebaseSessions              	       0x10d057098 protocol witness for SettingsProtocol.updateSettings() in conformance SessionsSettings + 24
36  FirebaseSessions              	       0x10d047eeb closure #1 in closure #1 in Sessions.init(appID:sessionGenerator:coordinator:initiator:appInfo:settings:loggedEventCallback:) + 363 (FirebaseSessions.swift:193)
37  FirebaseSessions              	       0x10d04bac3 partial apply for closure #1 in closure #1 in Sessions.init(appID:sessionGenerator:coordinator:initiator:appInfo:settings:loggedEventCallback:) + 35
38  Promises                      	       0x10ccff01c closure #1 in Promise.then(on:_:) + 348 (Promise+Then.swift:95)
39  Promises                      	       0x10ccfe29d thunk for @escaping @callee_guaranteed (@guaranteed Swift.AnyObject?) -> (@out Any?) + 61
40  FBLPromises                   	       0x10c69cf2d __56-[FBLPromise chainOnQueue:chainedFulfill:chainedReject:]_block_invoke.63 + 77 (FBLPromise.m:273)
41  FBLPromises                   	       0x10c69c5c7 __44-[FBLPromise observeOnQueue:fulfill:reject:]_block_invoke_2 + 103 (FBLPromise.m:226)
42  libdispatch.dylib             	    0x7ff800156a90 _dispatch_call_block_and_release + 12
43  libdispatch.dylib             	    0x7ff800157d3a _dispatch_client_callout + 8
44  libdispatch.dylib             	    0x7ff80016a827 _dispatch_root_queue_drain + 1405
45  libdispatch.dylib             	    0x7ff80016ae88 _dispatch_worker_thread2 + 244
46  libsystem_pthread.dylib       	       0x10da7ac0f _pthread_wqthread + 257
47  libsystem_pthread.dylib       	       0x10da79bbf start_wqthread + 15

Thread 3:: com.apple.uikit.eventfetch-thread
0   libsystem_kernel.dylib        	       0x110e7f552 mach_msg2_trap + 10
1   libsystem_kernel.dylib        	       0x110e8d6cd mach_msg2_internal + 78
2   libsystem_kernel.dylib        	       0x110e86584 mach_msg_overwrite + 692
3   libsystem_kernel.dylib        	       0x110e7f83a mach_msg + 19
4   CoreFoundation                	    0x7ff8003f7b33 __CFRunLoopServiceMachPort + 143
5   CoreFoundation                	    0x7ff8003f22bb __CFRunLoopRun + 1371
6   CoreFoundation                	    0x7ff8003f197d CFRunLoopRunSpecific + 557
7   Foundation                    	    0x7ff800e7ab9a -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 213
8   Foundation                    	    0x7ff800e7ae18 -[NSRunLoop(NSRunLoop) runUntilDate:] + 72
9   UIKitCore                     	    0x7ff805c95603 -[UIEventFetcher threadMain] + 518
10  Foundation                    	    0x7ff800ea5345 __NSThread__start__ + 1024
11  libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
12  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 4::  Dispatch queue: GULLoggingClientQueue
0   GoogleUtilities               	       0x10d1320a0 __destroy_helper_block_e8_32s + 0
1   libsystem_blocks.dylib        	    0x7ff8000cd214 _call_dispose_helpers_excp + 45
2   libsystem_blocks.dylib        	    0x7ff8000cdea5 _Block_release + 232
3   libdispatch.dylib             	    0x7ff800157d3a _dispatch_client_callout + 8
4   libdispatch.dylib             	    0x7ff80015f85c _dispatch_lane_serial_drain + 1236
5   libdispatch.dylib             	    0x7ff8001603f7 _dispatch_lane_invoke + 406
6   libdispatch.dylib             	    0x7ff80016bfac _dispatch_root_queue_drain_deferred_wlh + 276
7   libdispatch.dylib             	    0x7ff80016b572 _dispatch_workloop_worker_thread + 552
8   libsystem_pthread.dylib       	       0x10da7ac55 _pthread_wqthread + 327
9   libsystem_pthread.dylib       	       0x10da79bbf start_wqthread + 15

Thread 5:: io.flutter.1.ui
0   Flutter                       	       0x11eba2e16 dart::ScavengerVisitorBase<true>::VisitPointers(dart::ObjectPtr*, dart::ObjectPtr*) + 86
1   Flutter                       	       0x11eafa1cd dart::UntaggedObject::VisitPointersPredefined(dart::ObjectPointerVisitor*, long) + 1005
2   Flutter                       	       0x11eb9e6ae dart::ScavengerVisitorBase<true>::ProcessToSpace() + 94
3   Flutter                       	       0x11eb9d278 dart::ScavengerVisitorBase<true>::ProcessSurvivors() + 200
4   Flutter                       	       0x11eb9cd55 dart::ParallelScavengerTask::RunEnteredIsolateGroup() + 133
5   Flutter                       	       0x11eb983f4 dart::SafepointTask::RunBlockedAtSafepoint() + 116
6   Flutter                       	       0x11eb97e8a dart::SafepointHandler::ExitSafepointLocked(dart::Thread*, dart::MonitorLocker*, dart::SafepointLevel) + 154
7   Flutter                       	       0x11eb98347 dart::SafepointHandler::BlockForSafepoint(dart::Thread*) + 215
8   Flutter                       	       0x11ec900ad dart::CompilerPass::Run(dart::CompilerPassState*) const + 621
9   Flutter                       	       0x11eb76012 dart::CompileParsedFunctionHelper::Compile(dart::CompilationPipeline*) + 2002
10  Flutter                       	       0x11eb768a5 dart::CompileFunctionHelper(dart::CompilationPipeline*, dart::Function const&, bool, long) + 1125
11  Flutter                       	       0x11eb7641d dart::Compiler::CompileFunction(dart::Thread*, dart::Function const&) + 253
12  Flutter                       	       0x11ea93aa4 dart::Function::EnsureHasCodeNoThrow() const + 68
13  Flutter                       	       0x11ea939fd dart::Function::EnsureHasCode() const + 61
14  Flutter                       	       0x11eb75090 dart::DRT_CompileFunction(dart::NativeArguments) + 336
15  ???                           	       0x121483093 ???
16  ???                           	       0x1214830f4 ???
17  ???                           	       0x139e37e1f ???
18  ???                           	       0x139e29bec ???
19  ???                           	       0x139e28ae1 ???
20  ???                           	       0x139e2b9d9 ???
21  ???                           	       0x139e28ae1 ???
22  ???                           	       0x1436dbe51 ???
23  ???                           	       0x1329225a6 ???
24  ???                           	       0x132921e7d ???
25  ???                           	       0x1436dbd70 ???
26  ???                           	       0x1436db792 ???
27  ???                           	       0x1436db5f3 ???
28  ???                           	       0x1436db47e ???
29  ???                           	       0x1436db34b ???
30  ???                           	       0x1358c3fc6 ???
31  ???                           	       0x1358d18b6 ???
32  ???                           	       0x1358d1809 ???
33  ???                           	       0x1358dd148 ???
34  ???                           	       0x1358d9297 ???
35  ???                           	       0x1358c3d78 ???
36  ???                           	       0x1358fbfb4 ???
37  ???                           	       0x1358c3d78 ???
38  ???                           	       0x1358c4c38 ???
39  ???                           	       0x1358c3d78 ???
40  ???                           	       0x1358c4c38 ???
41  ???                           	       0x1358c3d78 ???
42  ???                           	       0x1358c4c38 ???
43  ???                           	       0x1358c3d78 ???
44  ???                           	       0x1358c4c38 ???
45  ???                           	       0x1358c3d78 ???
46  ???                           	       0x1358c4c38 ???
47  ???                           	       0x1358c3d78 ???
48  ???                           	       0x1358c4c38 ???
49  ???                           	       0x1358c3d78 ???
50  ???                           	       0x1358c4c38 ???
51  ???                           	       0x1358fd3c8 ???
52  ???                           	       0x1358c3d78 ???
53  ???                           	       0x1358c4c38 ???
54  ???                           	       0x1358d1a4a ???
55  ???                           	       0x1358c3d78 ???
56  ???                           	       0x1358fbfb4 ???
57  ???                           	       0x1358c3d78 ???
58  ???                           	       0x1358c4c38 ???
59  ???                           	       0x1358c3d78 ???
60  ???                           	       0x1358c4c38 ???
61  ???                           	       0x1358c3d78 ???
62  ???                           	       0x1358c4c38 ???
63  ???                           	       0x1358c3d78 ???
64  ???                           	       0x14319ba87 ???
65  ???                           	       0x1358c3d78 ???
66  ???                           	       0x137f3c5bf ???
67  ???                           	       0x1431994ab ???
68  ???                           	       0x1358c3d78 ???
69  ???                           	       0x137f3757c ???
70  ???                           	       0x137f35bd7 ???
71  ???                           	       0x137f32ed3 ???
72  ???                           	       0x1358c3d78 ???
73  ???                           	       0x1358c4c38 ???
74  ???                           	       0x1358c3d78 ???
75  ???                           	       0x1358c4c38 ???
76  ???                           	       0x1358c3d78 ???
77  ???                           	       0x1358c4c38 ???
78  ???                           	       0x1358c3d78 ???
79  ???                           	       0x1358c4c38 ???
80  ???                           	       0x1358c3d78 ???
81  ???                           	       0x1358c4c38 ???
82  ???                           	       0x1358c3d78 ???
83  ???                           	       0x1358c4c38 ???
84  ???                           	       0x1358c3d78 ???
85  ???                           	       0x1358ce537 ???
86  ???                           	       0x1358c3d78 ???
87  ???                           	       0x1358d18b6 ???
88  ???                           	       0x1358d1809 ???
89  ???                           	       0x1358dd148 ???
90  ???                           	       0x1358d9297 ???
91  ???                           	       0x1358c3d78 ???
92  ???                           	       0x1358d18b6 ???
93  ???                           	       0x1358d1809 ???
94  ???                           	       0x1358dd148 ???
95  ???                           	       0x1358d9297 ???
96  ???                           	       0x1358c3d78 ???
97  ???                           	       0x1358fbfb4 ???
98  ???                           	       0x1358c3d78 ???
99  ???                           	       0x13a078bce ???
100 ???                           	       0x1358c3d78 ???
101 ???                           	       0x1358c4c38 ???
102 ???                           	       0x1358c3d78 ???
103 ???                           	       0x1358c4c38 ???
104 ???                           	       0x1358c3d78 ???
105 ???                           	       0x1358c4c38 ???
106 ???                           	       0x1358c3d78 ???
107 ???                           	       0x1358c4c38 ???
108 ???                           	       0x1358c3d78 ???
109 ???                           	       0x1358c4c38 ???
110 ???                           	       0x1358c3d78 ???
111 ???                           	       0x1358c4c38 ???
112 ???                           	       0x1358c3d78 ???
113 ???                           	       0x1358d18b6 ???
114 ???                           	       0x1358d1809 ???
115 ???                           	       0x1358d0ed4 ???
116 ???                           	       0x1358d0160 ???
117 ???                           	       0x1358c3d78 ???
118 ???                           	       0x1358d18b6 ???
119 ???                           	       0x1358d1809 ???
120 ???                           	       0x1358d0ed4 ???
121 ???                           	       0x1358d0160 ???
122 ???                           	       0x1358c3d78 ???
123 ???                           	       0x1358c4c38 ???
124 ???                           	       0x1358c3d78 ???
125 ???                           	       0x1358fbfb4 ???
126 ???                           	       0x1358c3d78 ???
127 ???                           	       0x1358c4c38 ???
128 ???                           	       0x1358c3d78 ???
129 ???                           	       0x1358c4c38 ???
130 ???                           	       0x1358c3d78 ???
131 ???                           	       0x1358d18b6 ???
132 ???                           	       0x1358d1809 ???
133 ???                           	       0x1358d0ed4 ???
134 ???                           	       0x1358d0160 ???
135 ???                           	       0x1358c3d78 ???
136 ???                           	       0x1358d7b0c ???
137 ???                           	       0x1358d46ca ???
138 ???                           	       0x1358d28b7 ???
139 ???                           	       0x1358d1cc7 ???
140 ???                           	       0x1358c0e5b ???
141 ???                           	       0x132e0dd34 ???
142 ???                           	       0x132e0e033 ???
143 ???                           	       0x132e0d0c5 ???
144 ???                           	       0x1398f0b89 ???
145 ???                           	       0x1398e5d71 ???
146 ???                           	       0x121483a50 ???
147 ???                           	       0x1398e512f ???
148 ???                           	       0x13a301a81 ???
149 ???                           	       0x13a3017e9 ???
150 ???                           	       0x121483546 ???
151 Flutter                       	       0x11ea126ad dart::DartEntry::InvokeFunction(dart::Function const&, dart::Array const&, dart::Array const&) + 365
152 Flutter                       	       0x11ea12baa dart::DartEntry::InvokeCallable(dart::Thread*, dart::Function const&, dart::Array const&, dart::Array const&) + 266
153 Flutter                       	       0x11ed62b6e Dart_InvokeClosure + 1022
154 Flutter                       	       0x11e971db5 tonic::DartInvokeVoid(_Dart_Handle*) + 15
155 Flutter                       	       0x11e8823cb flutter::PlatformConfiguration::BeginFrame(fml::TimePoint, unsigned long long) + 655
156 Flutter                       	       0x11e3fe695 flutter::RuntimeController::BeginFrame(fml::TimePoint, unsigned long long) + 61
157 Flutter                       	       0x11e9747a6 flutter::Animator::BeginFrame(std::_fl::unique_ptr<flutter::FrameTimingsRecorder, std::_fl::default_delete<flutter::FrameTimingsRecorder>>) + 554
158 Flutter                       	       0x11e977a03 std::_fl::__function::__func<flutter::Animator::AwaitVSync()::$_0, std::_fl::allocator<flutter::Animator::AwaitVSync()::$_0>, void (std::_fl::unique_ptr<flutter::FrameTimingsRecorder, std::_fl::default_delete<flutter::FrameTimingsRecorder>>)>::operator()(std::_fl::unique_ptr<flutter::FrameTimingsRecorder, std::_fl::default_delete<flutter::FrameTimingsRecorder>>&&) + 69
159 Flutter                       	       0x11e99c392 std::_fl::__function::__func<flutter::VsyncWaiter::FireCallback(fml::TimePoint, fml::TimePoint, bool)::$_0, std::_fl::allocator<flutter::VsyncWaiter::FireCallback(fml::TimePoint, fml::TimePoint, bool)::$_0>, void ()>::operator()() + 130
160 Flutter                       	       0x11e38a54c fml::MessageLoopImpl::FlushTasks(fml::FlushType) + 156
161 Flutter                       	       0x11e390a8c fml::MessageLoopDarwin::OnTimerFire(__CFRunLoopTimer*, fml::MessageLoopDarwin*) + 26
162 CoreFoundation                	    0x7ff8003f8a19 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__ + 20
163 CoreFoundation                	    0x7ff8003f85a6 __CFRunLoopDoTimer + 801
164 CoreFoundation                	    0x7ff8003f7d2a __CFRunLoopDoTimers + 243
165 CoreFoundation                	    0x7ff8003f259c __CFRunLoopRun + 2108
166 CoreFoundation                	    0x7ff8003f197d CFRunLoopRunSpecific + 557
167 Flutter                       	       0x11e390bc9 fml::MessageLoopDarwin::Run() + 65
168 Flutter                       	       0x11e38a46a fml::MessageLoopImpl::DoRun() + 22
169 Flutter                       	       0x11e38fa79 std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()() + 135
170 Flutter                       	       0x11e38f81b fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 27
171 libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
172 libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 6:: io.flutter.1.raster
0   libsystem_kernel.dylib        	       0x110e7f552 mach_msg2_trap + 10
1   libsystem_kernel.dylib        	       0x110e8d6cd mach_msg2_internal + 78
2   libsystem_kernel.dylib        	       0x110e86584 mach_msg_overwrite + 692
3   libsystem_kernel.dylib        	       0x110e7f83a mach_msg + 19
4   CoreFoundation                	    0x7ff8003f7b33 __CFRunLoopServiceMachPort + 143
5   CoreFoundation                	    0x7ff8003f22bb __CFRunLoopRun + 1371
6   CoreFoundation                	    0x7ff8003f197d CFRunLoopRunSpecific + 557
7   Flutter                       	       0x11e390bc9 fml::MessageLoopDarwin::Run() + 65
8   Flutter                       	       0x11e38a46a fml::MessageLoopImpl::DoRun() + 22
9   Flutter                       	       0x11e38fa79 std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()() + 135
10  Flutter                       	       0x11e38f81b fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 27
11  libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
12  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 7:: io.flutter.1.io
0   libsystem_kernel.dylib        	       0x110e7f552 mach_msg2_trap + 10
1   libsystem_kernel.dylib        	       0x110e8d6cd mach_msg2_internal + 78
2   libsystem_kernel.dylib        	       0x110e86584 mach_msg_overwrite + 692
3   libsystem_kernel.dylib        	       0x110e7f83a mach_msg + 19
4   CoreFoundation                	    0x7ff8003f7b33 __CFRunLoopServiceMachPort + 143
5   CoreFoundation                	    0x7ff8003f22bb __CFRunLoopRun + 1371
6   CoreFoundation                	    0x7ff8003f197d CFRunLoopRunSpecific + 557
7   Flutter                       	       0x11e390bc9 fml::MessageLoopDarwin::Run() + 65
8   Flutter                       	       0x11e38a46a fml::MessageLoopImpl::DoRun() + 22
9   Flutter                       	       0x11e38fa79 std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()() + 135
10  Flutter                       	       0x11e38f81b fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 27
11  libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
12  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 8:: io.flutter.1.profiler
0   libsystem_kernel.dylib        	       0x110e7f552 mach_msg2_trap + 10
1   libsystem_kernel.dylib        	       0x110e8d6cd mach_msg2_internal + 78
2   libsystem_kernel.dylib        	       0x110e86584 mach_msg_overwrite + 692
3   libsystem_kernel.dylib        	       0x110e7f83a mach_msg + 19
4   CoreFoundation                	    0x7ff8003f7b33 __CFRunLoopServiceMachPort + 143
5   CoreFoundation                	    0x7ff8003f22bb __CFRunLoopRun + 1371
6   CoreFoundation                	    0x7ff8003f197d CFRunLoopRunSpecific + 557
7   Flutter                       	       0x11e390bc9 fml::MessageLoopDarwin::Run() + 65
8   Flutter                       	       0x11e38a46a fml::MessageLoopImpl::DoRun() + 22
9   Flutter                       	       0x11e38fa79 std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()() + 135
10  Flutter                       	       0x11e38f81b fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 27
11  libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
12  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 9:: io.worker.1
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   Flutter                       	       0x11e36a7c0 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 18
3   Flutter                       	       0x11e386be9 fml::ConcurrentMessageLoop::WorkerMain() + 167
4   Flutter                       	       0x11e38750b void* std::_fl::__thread_proxy[abi:v15000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 191
5   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
6   libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 10:: io.worker.2
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   Flutter                       	       0x11e36a7c0 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 18
3   Flutter                       	       0x11e386be9 fml::ConcurrentMessageLoop::WorkerMain() + 167
4   Flutter                       	       0x11e38750b void* std::_fl::__thread_proxy[abi:v15000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 191
5   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
6   libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 11:: io.worker.3
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   Flutter                       	       0x11e36a7c0 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 18
3   Flutter                       	       0x11e386be9 fml::ConcurrentMessageLoop::WorkerMain() + 167
4   Flutter                       	       0x11e38750b void* std::_fl::__thread_proxy[abi:v15000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 191
5   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
6   libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 12:: io.worker.4
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   Flutter                       	       0x11e36a7c0 std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 18
3   Flutter                       	       0x11e386be9 fml::ConcurrentMessageLoop::WorkerMain() + 167
4   Flutter                       	       0x11e38750b void* std::_fl::__thread_proxy[abi:v15000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 191
5   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
6   libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 13:: dart:io EventHandler
0   libsystem_kernel.dylib        	       0x110e8418a kevent + 10
1   Flutter                       	       0x11e94f908 dart::bin::EventHandlerImplementation::EventHandlerEntry(unsigned long) + 312
2   Flutter                       	       0x11e96bb73 dart::bin::ThreadStart(void*) + 83
3   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
4   libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 14:: Dart Profiler ThreadInterrupter
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e78d _pthread_cond_wait + 1295
2   Flutter                       	       0x11e9c9fee dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 126
3   Flutter                       	       0x11eb63322 dart::ThreadInterrupter::ThreadMain(unsigned long) + 322
4   Flutter                       	       0x11eaec08e dart::ThreadStart(void*) + 206
5   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
6   libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 15:: Dart Profiler SampleBlockProcessor
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e78d _pthread_cond_wait + 1295
2   Flutter                       	       0x11e9c9fee dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 126
3   Flutter                       	       0x11eaf127e dart::SampleBlockProcessor::ThreadMain(unsigned long) + 270
4   Flutter                       	       0x11eaec08e dart::ThreadStart(void*) + 206
5   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
6   libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 16:: DartWorker
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e78d _pthread_cond_wait + 1295
2   Flutter                       	       0x11e9c9fee dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 126
3   Flutter                       	       0x11ea2d99d dart::MutatorThreadPool::OnEnterIdleLocked(dart::MutexLocker*, dart::ThreadPool::Worker*) + 253
4   Flutter                       	       0x11eb64000 dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 112
5   Flutter                       	       0x11eb64333 dart::ThreadPool::Worker::Main(unsigned long) + 115
6   Flutter                       	       0x11eaec08e dart::ThreadStart(void*) + 206
7   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
8   libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 17:: com.apple.NSURLConnectionLoader
0   libsystem_kernel.dylib        	       0x110e7f552 mach_msg2_trap + 10
1   libsystem_kernel.dylib        	       0x110e8d6cd mach_msg2_internal + 78
2   libsystem_kernel.dylib        	       0x110e86584 mach_msg_overwrite + 692
3   libsystem_kernel.dylib        	       0x110e7f83a mach_msg + 19
4   CoreFoundation                	    0x7ff8003f7b33 __CFRunLoopServiceMachPort + 143
5   CoreFoundation                	    0x7ff8003f22bb __CFRunLoopRun + 1371
6   CoreFoundation                	    0x7ff8003f197d CFRunLoopRunSpecific + 557
7   CFNetwork                     	    0x7ff8048e704b 0x7ff8046b1000 + 2318411
8   Foundation                    	    0x7ff800ea5345 __NSThread__start__ + 1024
9   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
10  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 18::  Dispatch queue: APMAnalyticsQueue
0   libsystem_kernel.dylib        	       0x110e80cce __ulock_wait + 10
1   libsystem_platform.dylib      	       0x10d962b07 _os_unfair_lock_lock_slow + 162
2   libobjc.A.dylib               	    0x7ff80005623d lookUpImpOrForward + 92
3   libobjc.A.dylib               	    0x7ff80003e69b _objc_msgSend_uncached + 75
4   Runner                        	       0x10735f517 __65+[APMAnalytics logEventWithOrigin:isPublicEvent:name:parameters:]_block_invoke_3 + 59
5   libdispatch.dylib             	    0x7ff800156a90 _dispatch_call_block_and_release + 12
6   libdispatch.dylib             	    0x7ff800157d3a _dispatch_client_callout + 8
7   libdispatch.dylib             	    0x7ff80015f85c _dispatch_lane_serial_drain + 1236
8   libdispatch.dylib             	    0x7ff8001603f7 _dispatch_lane_invoke + 406
9   libdispatch.dylib             	    0x7ff80016bfac _dispatch_root_queue_drain_deferred_wlh + 276
10  libdispatch.dylib             	    0x7ff80016b572 _dispatch_workloop_worker_thread + 552
11  libsystem_pthread.dylib       	       0x10da7ac55 _pthread_wqthread + 327
12  libsystem_pthread.dylib       	       0x10da79bbf start_wqthread + 15

Thread 19:: com.google.firebase.crashlytics.MachExceptionServer
0   libsystem_kernel.dylib        	       0x110e7f552 mach_msg2_trap + 10
1   libsystem_kernel.dylib        	       0x110e8d6cd mach_msg2_internal + 78
2   libsystem_kernel.dylib        	       0x110e86584 mach_msg_overwrite + 692
3   libsystem_kernel.dylib        	       0x110e7f83a mach_msg + 19
4   FirebaseCrashlytics           	       0x10cd7458e FIRCLSMachExceptionReadMessage + 78 (FIRCLSMachException.c:192)
5   FirebaseCrashlytics           	       0x10cd744d0 FIRCLSMachExceptionServer + 48 (FIRCLSMachException.c:168)
6   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
7   libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 20::  Dispatch queue: APMExperimentWorkerQueue
0   libobjc.A.dylib               	    0x7ff80003e6b0 _objc_msgSend_uncached + 96
1   Runner                        	       0x1073839f4 -[APMEDatabase initializeDatabaseResourcesWithContext:databasePath:] + 59
2   Runner                        	       0x107383934 -[APMEDatabase initWithPath:] + 147
3   Runner                        	       0x10738cf5b -[APMETaskManager startTaskManagerOnWorkerQueue] + 73
4   Runner                        	       0x10738cf03 __35-[APMETaskManager startTaskManager]_block_invoke + 34
5   Runner                        	       0x10738e661 __46-[APMETaskManager dispatchAsyncOnWorkerQueue:]_block_invoke + 28
6   libdispatch.dylib             	    0x7ff800156a90 _dispatch_call_block_and_release + 12
7   libdispatch.dylib             	    0x7ff800157d3a _dispatch_client_callout + 8
8   libdispatch.dylib             	    0x7ff80015f85c _dispatch_lane_serial_drain + 1236
9   libdispatch.dylib             	    0x7ff8001603f7 _dispatch_lane_invoke + 406
10  libdispatch.dylib             	    0x7ff80016bfac _dispatch_root_queue_drain_deferred_wlh + 276
11  libdispatch.dylib             	    0x7ff80016b572 _dispatch_workloop_worker_thread + 552
12  libsystem_pthread.dylib       	       0x10da7ac55 _pthread_wqthread + 327
13  libsystem_pthread.dylib       	       0x10da79bbf start_wqthread + 15

Thread 21:: DartWorker
0   Flutter                       	       0x11eba2f49 dart::ScavengerVisitorBase<true>::VisitPointers(dart::ObjectPtr*, dart::ObjectPtr*) + 393
1   Flutter                       	       0x11eb9e839 dart::ScavengerVisitorBase<true>::ProcessToSpace() + 489
2   Flutter                       	       0x11eb9d278 dart::ScavengerVisitorBase<true>::ProcessSurvivors() + 200
3   Flutter                       	       0x11eb9cd55 dart::ParallelScavengerTask::RunEnteredIsolateGroup() + 133
4   Flutter                       	       0x11eb985e8 dart::SafepointHandler::RunTasks(dart::IntrusiveDList<dart::SafepointTask, 1>*) + 376
5   Flutter                       	       0x11eb9b585 dart::Scavenger::ParallelScavenge(dart::SemiSpace*) + 485
6   Flutter                       	       0x11eb9b0f6 dart::Scavenger::Scavenge(dart::Thread*, dart::GCType, dart::GCReason) + 406
7   Flutter                       	       0x11eb867ce dart::Heap::CollectNewSpaceGarbage(dart::Thread*, dart::GCType, dart::GCReason) + 510
8   Flutter                       	       0x11eb85348 dart::Heap::AllocateNew(dart::Thread*, long) + 360
9   Flutter                       	       0x11ea7acea dart::Object::Allocate(long, long, dart::Heap::Space, bool, unsigned long, unsigned long) + 74
10  Flutter                       	       0x11eaafd98 dart::String::FromUTF8(unsigned char const*, long, dart::Heap::Space) + 280
11  Flutter                       	       0x11ecd4600 dart::kernel::TranslationHelper::DartString(dart::kernel::StringIndex, dart::Heap::Space) + 176
12  Flutter                       	       0x11ecd4af4 dart::kernel::TranslationHelper::DartFactoryName(dart::kernel::NameIndex) + 244
13  Flutter                       	       0x11ecd569e dart::kernel::TranslationHelper::LookupStaticMethodByKernelProcedure(dart::kernel::NameIndex, bool) + 30
14  Flutter                       	       0x11ecae670 dart::kernel::StreamingFlowGraphBuilder::BuildStaticInvocation(dart::TokenPosition*) + 144
15  Flutter                       	       0x11ecb48a5 dart::kernel::StreamingFlowGraphBuilder::BuildVariableDeclaration(dart::TokenPosition*) + 181
16  Flutter                       	       0x11ecb1ada dart::kernel::StreamingFlowGraphBuilder::BuildBlock(dart::TokenPosition*) + 170
17  Flutter                       	       0x11ecaa49e dart::kernel::StreamingFlowGraphBuilder::BuildFunctionBody(dart::Function const&, dart::LocalVariable*, bool) + 478
18  Flutter                       	       0x11ecaaaab dart::kernel::StreamingFlowGraphBuilder::BuildGraphOfFunction(bool) + 651
19  Flutter                       	       0x11ecaaec7 dart::kernel::StreamingFlowGraphBuilder::BuildGraph() + 311
20  Flutter                       	       0x11ecbd365 dart::kernel::FlowGraphBuilder::BuildGraph() + 101
21  Flutter                       	       0x11eb74cee dart::DartCompilationPipeline::BuildFlowGraph(dart::Zone*, dart::ParsedFunction*, dart::ZoneGrowableArray<dart::ICData const*>*, long, bool) + 62
22  Flutter                       	       0x11eb75dfd dart::CompileParsedFunctionHelper::Compile(dart::CompilationPipeline*) + 1469
23  Flutter                       	       0x11eb768a5 dart::CompileFunctionHelper(dart::CompilationPipeline*, dart::Function const&, bool, long) + 1125
24  Flutter                       	       0x11eb76f67 dart::Compiler::CompileOptimizedFunction(dart::Thread*, dart::Function const&, long) + 263
25  Flutter                       	       0x11eb7756a dart::BackgroundCompiler::Run() + 266
26  Flutter                       	       0x11eb640b6 dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 294
27  Flutter                       	       0x11eb64333 dart::ThreadPool::Worker::Main(unsigned long) + 115
28  Flutter                       	       0x11eaec08e dart::ThreadStart(void*) + 206
29  libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
30  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 22:
0   libsystem_pthread.dylib       	       0x10da79bb0 start_wqthread + 0

Thread 23::  Dispatch queue: com.apple.UIKit.UITextInputSessionActionAnalytics
0   libsystem_kernel.dylib        	       0x110e80cce __ulock_wait + 10
1   libsystem_platform.dylib      	       0x10d962b07 _os_unfair_lock_lock_slow + 162
2   libobjc.A.dylib               	    0x7ff80005623d lookUpImpOrForward + 92
3   libobjc.A.dylib               	    0x7ff80003e69b _objc_msgSend_uncached + 75
4   TextInput                     	    0x7ff81352d0c4 -[TIAnalyticsService dispatchEventWithName:values:inputMode:] + 46
5   UIKitCore                     	    0x7ff804f5c5ea __49+[UIKBAnalyticsDispatcher sessionAnalyticsEnded:]_block_invoke.152 + 355
6   UIKitCore                     	    0x7ff805f6ae14 -[_UITextInputSessionAccumulator enumerateAnalytics:] + 302
7   CoreFoundation                	    0x7ff8003b3524 __NSSET_IS_CALLING_OUT_TO_A_BLOCK__ + 7
8   CoreFoundation                	    0x7ff8004b82c8 -[__NSSetM enumerateObjectsWithOptions:usingBlock:] + 237
9   UIKitCore                     	    0x7ff8055f80de __56-[UITextInputSessionActionAnalytics enumerateAnalytics:]_block_invoke + 124
10  libdispatch.dylib             	    0x7ff800156a90 _dispatch_call_block_and_release + 12
11  libdispatch.dylib             	    0x7ff800157d3a _dispatch_client_callout + 8
12  libdispatch.dylib             	    0x7ff80015f85c _dispatch_lane_serial_drain + 1236
13  libdispatch.dylib             	    0x7ff8001603f7 _dispatch_lane_invoke + 406
14  libdispatch.dylib             	    0x7ff80016bfac _dispatch_root_queue_drain_deferred_wlh + 276
15  libdispatch.dylib             	    0x7ff80016b572 _dispatch_workloop_worker_thread + 552
16  libsystem_pthread.dylib       	       0x10da7ac55 _pthread_wqthread + 327
17  libsystem_pthread.dylib       	       0x10da79bbf start_wqthread + 15

Thread 24:
0   libsystem_pthread.dylib       	       0x10da79bb0 start_wqthread + 0

Thread 25::  Dispatch queue: com.google.fira.worker
0   Runner                        	       0x1073a5ffb -[APMMeasurement setEnabledOnWorkerQueue:] + 34
1   Runner                        	       0x1073a5fcc __29-[APMMeasurement setEnabled:]_block_invoke + 42
2   Runner                        	       0x10740ef95 __51-[APMScheduler scheduleOnWorkerQueueBlockID:block:]_block_invoke + 29
3   libdispatch.dylib             	    0x7ff800156a90 _dispatch_call_block_and_release + 12
4   libdispatch.dylib             	    0x7ff800157d3a _dispatch_client_callout + 8
5   libdispatch.dylib             	    0x7ff80015f85c _dispatch_lane_serial_drain + 1236
6   libdispatch.dylib             	    0x7ff8001603f7 _dispatch_lane_invoke + 406
7   libdispatch.dylib             	    0x7ff80016bfac _dispatch_root_queue_drain_deferred_wlh + 276
8   libdispatch.dylib             	    0x7ff80016b572 _dispatch_workloop_worker_thread + 552
9   libsystem_pthread.dylib       	       0x10da7ac55 _pthread_wqthread + 327
10  libsystem_pthread.dylib       	       0x10da79bbf start_wqthread + 15

Thread 26:
0   libsystem_pthread.dylib       	       0x10da79bb0 start_wqthread + 0

Thread 27::  Dispatch queue: APMMonitorQueue
0   libsystem_malloc.dylib        	    0x7ff8001be858 free + 0
1   libsystem_c.dylib             	    0x7ff800116785 __vfprintf + 16363
2   libsystem_c.dylib             	    0x7ff80011ff66 _vsnprintf + 330
3   libsystem_c.dylib             	    0x7ff80011ffc1 vsnprintf_l + 41
4   libsystem_c.dylib             	    0x7ff8001112ff snprintf_l + 123
5   CoreFoundation                	    0x7ff80041f42a __CFStringAppendFormatCore + 12203
6   CoreFoundation                	    0x7ff800420902 _CFStringCreateWithFormatAndArgumentsReturningMetadata + 170
7   CoreFoundation                	    0x7ff800420852 _CFStringCreateWithFormatAndArgumentsAux2 + 28
8   Foundation                    	    0x7ff800e98028 -[NSPlaceholderString initWithFormat:locale:arguments:] + 188
9   Foundation                    	    0x7ff800e90663 +[NSString stringWithFormat:] + 160
10  Runner                        	       0x107362409 -[APMASLLogger logMessage:logTag:messageCode:withLogLevel:] + 137
11  Runner                        	       0x1073c8d0b __44-[APMMonitor logToConsole:message:logLevel:]_block_invoke + 103
12  libdispatch.dylib             	    0x7ff800156a90 _dispatch_call_block_and_release + 12
13  libdispatch.dylib             	    0x7ff800157d3a _dispatch_client_callout + 8
14  libdispatch.dylib             	    0x7ff80015f85c _dispatch_lane_serial_drain + 1236
15  libdispatch.dylib             	    0x7ff8001603f7 _dispatch_lane_invoke + 406
16  libdispatch.dylib             	    0x7ff80016bfac _dispatch_root_queue_drain_deferred_wlh + 276
17  libdispatch.dylib             	    0x7ff80016b572 _dispatch_workloop_worker_thread + 552
18  libsystem_pthread.dylib       	       0x10da7ac55 _pthread_wqthread + 327
19  libsystem_pthread.dylib       	       0x10da79bbf start_wqthread + 15

Thread 28::  Dispatch queue: com.google.firebase.firestore.rpc
0   libsystem_kernel.dylib        	       0x110e86242 poll + 10
1   grpc                          	       0x1155aba20 pollset_work(grpc_pollset*, grpc_pollset_worker**, grpc_core::Timestamp) + 1984 (ev_poll_posix.cc:1020)
2   grpc                          	       0x1155aff7f pollset_work(grpc_pollset*, grpc_pollset_worker**, grpc_core::Timestamp) + 159 (ev_posix.cc:249)
3   grpc                          	       0x11580a740 grpc_pollset_work(grpc_pollset*, grpc_pollset_worker**, grpc_core::Timestamp) + 64 (pollset.cc:48)
4   grpc                          	       0x1154f604f cq_next(grpc_completion_queue*, gpr_timespec, void*) + 975 (completion_queue.cc:1043)
5   grpc                          	       0x1154f538d grpc_completion_queue_next + 61 (completion_queue.cc:1121)
6   grpcpp                        	       0x1123bde35 grpc::CompletionQueue::AsyncNextInternal(void**, bool*, gpr_timespec) + 69 (completion_queue_cc.cc:146)
7   FirebaseFirestoreInternal     	       0x10e51040d grpc::CompletionQueue::Next(void**, bool*) + 93 (completion_queue.h:182)
8   FirebaseFirestoreInternal     	       0x10e510303 firebase::firestore::remote::Datastore::PollGrpcQueue() + 179 (datastore.cc:143)
9   FirebaseFirestoreInternal     	       0x10e519f28 firebase::firestore::remote::Datastore::Start()::$_0::operator()() const + 24 (datastore.cc:116)
10  FirebaseFirestoreInternal     	       0x10e519f05 decltype(std::declval<firebase::firestore::remote::Datastore::Start()::$_0&>()()) std::__1::__invoke[abi:v160006]<firebase::firestore::remote::Datastore::Start()::$_0&>(firebase::firestore::remote::Datastore::Start()::$_0&) + 21 (invoke.h:394)
11  FirebaseFirestoreInternal     	       0x10e519ec5 void std::__1::__invoke_void_return_wrapper<void, true>::__call<firebase::firestore::remote::Datastore::Start()::$_0&>(firebase::firestore::remote::Datastore::Start()::$_0&) + 21 (invoke.h:487)
12  FirebaseFirestoreInternal     	       0x10e519e9d std::__1::__function::__alloc_func<firebase::firestore::remote::Datastore::Start()::$_0, std::__1::allocator<firebase::firestore::remote::Datastore::Start()::$_0>, void ()>::operator()[abi:v160006]() + 29 (function.h:185)
13  FirebaseFirestoreInternal     	       0x10e518eb9 std::__1::__function::__func<firebase::firestore::remote::Datastore::Start()::$_0, std::__1::allocator<firebase::firestore::remote::Datastore::Start()::$_0>, void ()>::operator()() + 25 (function.h:356)
14  FirebaseFirestoreInternal     	       0x10e49ac82 std::__1::__function::__value_func<void ()>::operator()[abi:v160006]() const + 50 (function.h:510)
15  FirebaseFirestoreInternal     	       0x10e498725 std::__1::function<void ()>::operator()() const + 21 (function.h:1156)
16  FirebaseFirestoreInternal     	       0x10e8cf2cd firebase::firestore::util::Task::ExecuteAndRelease() + 221 (task.cc:102)
17  FirebaseFirestoreInternal     	       0x10e57f86d firebase::firestore::util::ExecutorLibdispatch::InvokeAsync(void*) + 29 (executor_libdispatch.mm:237)
18  libdispatch.dylib             	    0x7ff800157d3a _dispatch_client_callout + 8
19  libdispatch.dylib             	    0x7ff80015f85c _dispatch_lane_serial_drain + 1236
20  libdispatch.dylib             	    0x7ff8001603f7 _dispatch_lane_invoke + 406
21  libdispatch.dylib             	    0x7ff80016bfac _dispatch_root_queue_drain_deferred_wlh + 276
22  libdispatch.dylib             	    0x7ff80016b572 _dispatch_workloop_worker_thread + 552
23  libsystem_pthread.dylib       	       0x10da7ac55 _pthread_wqthread + 327
24  libsystem_pthread.dylib       	       0x10da79bbf start_wqthread + 15

Thread 29:
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   grpc                          	       0x115a0f9cd gpr_cv_wait + 109 (sync.cc:114)
3   grpc                          	       0x1155c816a grpc_core::Executor::ThreadMain(void*) + 458 (executor.cc:230)
4   grpc                          	       0x115a399b1 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const + 225 (thd.cc:148)
5   grpc                          	       0x115a398c9 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*) + 25 (thd.cc:118)
6   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
7   libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 30:
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   grpc                          	       0x115a0f9cd gpr_cv_wait + 109 (sync.cc:114)
3   grpc                          	       0x1155c816a grpc_core::Executor::ThreadMain(void*) + 458 (executor.cc:230)
4   grpc                          	       0x115a399b1 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const + 225 (thd.cc:148)
5   grpc                          	       0x115a398c9 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*) + 25 (thd.cc:118)
6   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
7   libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 31:
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   grpc                          	       0x115a0f9cd gpr_cv_wait + 109 (sync.cc:114)
3   grpc                          	       0x115a51d5b wait_until(grpc_core::Timestamp) + 491 (timer_manager.cc:204)
4   grpc                          	       0x115a518ef timer_main_loop() + 191 (timer_manager.cc:258)
5   grpc                          	       0x115a517ef timer_thread(void*) + 31 (timer_manager.cc:287)
6   grpc                          	       0x115a399b1 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const + 225 (thd.cc:148)
7   grpc                          	       0x115a398c9 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*) + 25 (thd.cc:118)
8   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
9   libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 32:
0   libsystem_pthread.dylib       	       0x10da79bb0 start_wqthread + 0

Thread 33:
0   libsystem_pthread.dylib       	       0x10da79bb0 start_wqthread + 0

Thread 34:
0   libsystem_pthread.dylib       	       0x10da79bb0 start_wqthread + 0

Thread 35::  Dispatch queue: com.apple.assistant.analytics.client
0   AssistantServices             	    0x7ff8153c26d2 ___AFMachAbsoluteTimeRate_block_invoke + 0
1   libdispatch.dylib             	    0x7ff800157d3a _dispatch_client_callout + 8
2   libdispatch.dylib             	    0x7ff8001590f3 _dispatch_once_callout + 20
3   AssistantServices             	    0x7ff81534d6f1 _AFAnalyticsEventCreate + 641
4   AssistantServices             	    0x7ff81534dafb __71-[AFAnalytics logEventWithType:machAbsoluteTime:context:contextNoCopy:]_block_invoke + 27
5   libdispatch.dylib             	    0x7ff800156a90 _dispatch_call_block_and_release + 12
6   libdispatch.dylib             	    0x7ff800157d3a _dispatch_client_callout + 8
7   libdispatch.dylib             	    0x7ff80015f85c _dispatch_lane_serial_drain + 1236
8   libdispatch.dylib             	    0x7ff8001603f7 _dispatch_lane_invoke + 406
9   libdispatch.dylib             	    0x7ff80016bfac _dispatch_root_queue_drain_deferred_wlh + 276
10  libdispatch.dylib             	    0x7ff80016b572 _dispatch_workloop_worker_thread + 552
11  libsystem_pthread.dylib       	       0x10da7ac55 _pthread_wqthread + 327
12  libsystem_pthread.dylib       	       0x10da79bbf start_wqthread + 15

Thread 36:
0   libsystem_pthread.dylib       	       0x10da79bb0 start_wqthread + 0

Thread 37:
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   grpc                          	       0x115a0fa9b gpr_cv_wait + 315 (sync.cc:129)
3   grpc                          	       0x1155d992a grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration) + 122 (sync.h:139)
4   grpc                          	       0x115abafd2 grpc_event_engine::experimental::WorkStealingThreadPool::WorkSignal::WaitWithTimeout(grpc_core::Duration) + 130 (work_stealing_thread_pool.cc:627)
5   grpc                          	       0x115abaab2 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step() + 482 (work_stealing_thread_pool.cc:562)
6   grpc                          	       0x115aba5b9 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody() + 201 (work_stealing_thread_pool.cc:495)
7   grpc                          	       0x115abc8d1 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const + 33 (work_stealing_thread_pool.cc:260)
8   grpc                          	       0x115abc8a9 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*) + 25 (work_stealing_thread_pool.cc:258)
9   grpc                          	       0x115a399b1 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const + 225 (thd.cc:148)
10  grpc                          	       0x115a398c9 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*) + 25 (thd.cc:118)
11  libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
12  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 38:
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   grpc                          	       0x115a0fa9b gpr_cv_wait + 315 (sync.cc:129)
3   grpc                          	       0x1155d992a grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration) + 122 (sync.h:139)
4   grpc                          	       0x115abafd2 grpc_event_engine::experimental::WorkStealingThreadPool::WorkSignal::WaitWithTimeout(grpc_core::Duration) + 130 (work_stealing_thread_pool.cc:627)
5   grpc                          	       0x115abaab2 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step() + 482 (work_stealing_thread_pool.cc:562)
6   grpc                          	       0x115aba5b9 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody() + 201 (work_stealing_thread_pool.cc:495)
7   grpc                          	       0x115abc8d1 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const + 33 (work_stealing_thread_pool.cc:260)
8   grpc                          	       0x115abc8a9 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*) + 25 (work_stealing_thread_pool.cc:258)
9   grpc                          	       0x115a399b1 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const + 225 (thd.cc:148)
10  grpc                          	       0x115a398c9 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*) + 25 (thd.cc:118)
11  libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
12  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 39:
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   grpc                          	       0x115a0fa9b gpr_cv_wait + 315 (sync.cc:129)
3   grpc                          	       0x1155d992a grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration) + 122 (sync.h:139)
4   grpc                          	       0x115abafd2 grpc_event_engine::experimental::WorkStealingThreadPool::WorkSignal::WaitWithTimeout(grpc_core::Duration) + 130 (work_stealing_thread_pool.cc:627)
5   grpc                          	       0x115abaab2 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step() + 482 (work_stealing_thread_pool.cc:562)
6   grpc                          	       0x115aba5b9 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody() + 201 (work_stealing_thread_pool.cc:495)
7   grpc                          	       0x115abc8d1 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const + 33 (work_stealing_thread_pool.cc:260)
8   grpc                          	       0x115abc8a9 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*) + 25 (work_stealing_thread_pool.cc:258)
9   grpc                          	       0x115a399b1 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const + 225 (thd.cc:148)
10  grpc                          	       0x115a398c9 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*) + 25 (thd.cc:118)
11  libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
12  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 40:
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   grpc                          	       0x115a0fa9b gpr_cv_wait + 315 (sync.cc:129)
3   grpc                          	       0x1155d992a grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration) + 122 (sync.h:139)
4   grpc                          	       0x115a4f0b4 grpc_event_engine::experimental::TimerManager::WaitUntil(grpc_core::Timestamp) + 292 (timer_manager.cc:60)
5   grpc                          	       0x115a50b2e grpc_event_engine::experimental::TimerManager::MainLoop()::$_0::operator()() const + 62 (timer_manager.cc:79)
6   grpc                          	       0x115a50ae5 decltype(std::declval<grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&>()()) absl::lts_20240116::base_internal::Callable::Invoke<grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&>(grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&) + 21 (invoke.h:185)
7   grpc                          	       0x115a50ac5 decltype(Invoker<grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&>::type::Invoke(std::declval<grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&>())) absl::lts_20240116::base_internal::invoke<grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&>(grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&) + 21 (invoke.h:212)
8   grpc                          	       0x115a50aa5 void absl::lts_20240116::internal_any_invocable::InvokeR<void, grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&, void>(grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&) + 21 (any_invocable.h:132)
9   grpc                          	       0x115a506c0 void absl::lts_20240116::internal_any_invocable::RemoteInvoker<false, void, grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&>(absl::lts_20240116::internal_any_invocable::TypeErasedState*) + 32 (any_invocable.h:368)
10  grpc                          	       0x1152c2fbe absl::lts_20240116::internal_any_invocable::Impl<void ()>::operator()() + 94 (any_invocable.h:868)
11  grpc                          	       0x1152c2e5d grpc_event_engine::experimental::SelfDeletingClosure::Run() + 29 (common_closures.h:56)
12  grpc                          	       0x115abac3f grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step() + 879 (work_stealing_thread_pool.cc:581)
13  grpc                          	       0x115aba5b9 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody() + 201 (work_stealing_thread_pool.cc:495)
14  grpc                          	       0x115abc8d1 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const + 33 (work_stealing_thread_pool.cc:260)
15  grpc                          	       0x115abc8a9 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*) + 25 (work_stealing_thread_pool.cc:258)
16  grpc                          	       0x115a399b1 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const + 225 (thd.cc:148)
17  grpc                          	       0x115a398c9 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*) + 25 (thd.cc:118)
18  libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
19  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 41:
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   grpc                          	       0x115a0fa9b gpr_cv_wait + 315 (sync.cc:129)
3   grpc                          	       0x1155d992a grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration) + 122 (sync.h:139)
4   grpc                          	       0x115abafd2 grpc_event_engine::experimental::WorkStealingThreadPool::WorkSignal::WaitWithTimeout(grpc_core::Duration) + 130 (work_stealing_thread_pool.cc:627)
5   grpc                          	       0x115abaab2 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step() + 482 (work_stealing_thread_pool.cc:562)
6   grpc                          	       0x115aba5b9 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody() + 201 (work_stealing_thread_pool.cc:495)
7   grpc                          	       0x115abc8d1 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const + 33 (work_stealing_thread_pool.cc:260)
8   grpc                          	       0x115abc8a9 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*) + 25 (work_stealing_thread_pool.cc:258)
9   grpc                          	       0x115a399b1 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const + 225 (thd.cc:148)
10  grpc                          	       0x115a398c9 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*) + 25 (thd.cc:118)
11  libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
12  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 42:
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   grpc                          	       0x115a0fa9b gpr_cv_wait + 315 (sync.cc:129)
3   grpc                          	       0x1155d992a grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration) + 122 (sync.h:139)
4   grpc                          	       0x115abafd2 grpc_event_engine::experimental::WorkStealingThreadPool::WorkSignal::WaitWithTimeout(grpc_core::Duration) + 130 (work_stealing_thread_pool.cc:627)
5   grpc                          	       0x115abaab2 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step() + 482 (work_stealing_thread_pool.cc:562)
6   grpc                          	       0x115aba5b9 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody() + 201 (work_stealing_thread_pool.cc:495)
7   grpc                          	       0x115abc8d1 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const + 33 (work_stealing_thread_pool.cc:260)
8   grpc                          	       0x115abc8a9 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*) + 25 (work_stealing_thread_pool.cc:258)
9   grpc                          	       0x115a399b1 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const + 225 (thd.cc:148)
10  grpc                          	       0x115a398c9 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*) + 25 (thd.cc:118)
11  libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
12  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 43:
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   grpc                          	       0x115a0fa9b gpr_cv_wait + 315 (sync.cc:129)
3   grpc                          	       0x1155d992a grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration) + 122 (sync.h:139)
4   grpc                          	       0x115abafd2 grpc_event_engine::experimental::WorkStealingThreadPool::WorkSignal::WaitWithTimeout(grpc_core::Duration) + 130 (work_stealing_thread_pool.cc:627)
5   grpc                          	       0x115abaab2 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step() + 482 (work_stealing_thread_pool.cc:562)
6   grpc                          	       0x115aba5b9 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody() + 201 (work_stealing_thread_pool.cc:495)
7   grpc                          	       0x115abc8d1 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const + 33 (work_stealing_thread_pool.cc:260)
8   grpc                          	       0x115abc8a9 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*) + 25 (work_stealing_thread_pool.cc:258)
9   grpc                          	       0x115a399b1 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const + 225 (thd.cc:148)
10  grpc                          	       0x115a398c9 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*) + 25 (thd.cc:118)
11  libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
12  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 44:
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   grpc                          	       0x115a0fa9b gpr_cv_wait + 315 (sync.cc:129)
3   grpc                          	       0x1155d992a grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration) + 122 (sync.h:139)
4   grpc                          	       0x115abafd2 grpc_event_engine::experimental::WorkStealingThreadPool::WorkSignal::WaitWithTimeout(grpc_core::Duration) + 130 (work_stealing_thread_pool.cc:627)
5   grpc                          	       0x115abaab2 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step() + 482 (work_stealing_thread_pool.cc:562)
6   grpc                          	       0x115aba5b9 grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody() + 201 (work_stealing_thread_pool.cc:495)
7   grpc                          	       0x115abc8d1 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const + 33 (work_stealing_thread_pool.cc:260)
8   grpc                          	       0x115abc8a9 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*) + 25 (work_stealing_thread_pool.cc:258)
9   grpc                          	       0x115a399b1 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const + 225 (thd.cc:148)
10  grpc                          	       0x115a398c9 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*) + 25 (thd.cc:118)
11  libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
12  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 45:
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   grpc                          	       0x115a0fa9b gpr_cv_wait + 315 (sync.cc:129)
3   grpc                          	       0x1155d992a grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration) + 122 (sync.h:139)
4   grpc                          	       0x115ab9f45 grpc_core::Notification::WaitForNotificationWithTimeout(absl::lts_20240116::Duration) + 629 (notification.h:48)
5   grpc                          	       0x115ab9c61 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::Lifeguard::LifeguardMain() + 241 (work_stealing_thread_pool.cc:403)
6   grpc                          	       0x115abc991 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::Lifeguard::Start()::$_1::operator()(void*) const + 33 (work_stealing_thread_pool.cc:387)
7   grpc                          	       0x115abc969 grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::Lifeguard::Start()::$_1::__invoke(void*) + 25 (work_stealing_thread_pool.cc:385)
8   grpc                          	       0x115a399b1 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const + 225 (thd.cc:148)
9   grpc                          	       0x115a398c9 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*) + 25 (thd.cc:118)
10  libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
11  libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 46:
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e758 _pthread_cond_wait + 1242
2   grpc                          	       0x115a0fa9b gpr_cv_wait + 315 (sync.cc:129)
3   grpc                          	       0x115a51d5b wait_until(grpc_core::Timestamp) + 491 (timer_manager.cc:204)
4   grpc                          	       0x115a518ef timer_main_loop() + 191 (timer_manager.cc:258)
5   grpc                          	       0x115a517ef timer_thread(void*) + 31 (timer_manager.cc:287)
6   grpc                          	       0x115a399b1 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const + 225 (thd.cc:148)
7   grpc                          	       0x115a398c9 grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*) + 25 (thd.cc:118)
8   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
9   libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15

Thread 47:: DartWorker
0   libsystem_kernel.dylib        	       0x110e8208e __psynch_cvwait + 10
1   libsystem_pthread.dylib       	       0x10da7e78d _pthread_cond_wait + 1295
2   Flutter                       	       0x11e9c9fee dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 126
3   Flutter                       	       0x11eb641e8 dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 600
4   Flutter                       	       0x11eb64333 dart::ThreadPool::Worker::Main(unsigned long) + 115
5   Flutter                       	       0x11eaec08e dart::ThreadStart(void*) + 206
6   libsystem_pthread.dylib       	       0x10da7e1d3 _pthread_start + 125
7   libsystem_pthread.dylib       	       0x10da79bd3 thread_start + 15


Thread 0 crashed with X86 Thread State (64-bit):
  rax: 0x0000000000000000  rbx: 0x000000011b19d280  rcx: 0x00007ff7b8bb0f18  rdx: 0x0000000000000000
  rdi: 0x0000000000000103  rsi: 0x0000000000000006  rbp: 0x00007ff7b8bb0f40  rsp: 0x00007ff7b8bb0f18
   r8: 0x0000000000000000   r9: 0x0000000000989680  r10: 0x0000000000000000  r11: 0x0000000000000246
  r12: 0x0000000000000103  r13: 0x0000000000000003  r14: 0x0000000000000006  r15: 0x0000000000000016
  rip: 0x0000000110e86196  rfl: 0x0000000000000246  cr2: 0x0000000000000000
  
Logical CPU:     0
Error Code:      0x02000148 
Trap Number:     133


Binary Images:
       0x11b0f5000 -        0x11b190fff dyld (*) <d4fdfb7a-666b-3be3-969d-5728d56e85fd> /usr/lib/dyld
       0x129180000 -        0x1291b1fff com.apple.AutoFillUI (1.0) <c504f7ed-8ad4-3937-89a7-e578d91f8ffc> /Volumes/VOLUME/*/AutoFillUI.framework/AutoFillUI
       0x130c37000 -        0x130d89fff libquic.dylib (*) <d7aa814e-209f-325c-a7ff-247fac40a60d> /Volumes/VOLUME/*/libquic.dylib
       0x10c600000 -        0x10c636fff com.apple.MTLSimDriver (341.35) <b36bfdcc-4219-3c83-b4cf-ba66bc6a7336> /Volumes/VOLUME/*/MTLSimDriver.framework/MTLSimDriver
       0x110eea000 -        0x110f16fff com.apple.gpusw.MetalSerializer (1.0) <0b871c05-45c6-336a-9377-952af116b122> /Volumes/VOLUME/*/MetalSerializer.framework/MetalSerializer
       0x10d263000 -        0x10d26ffff libobjc-trampolines.dylib (*) <d90c52be-fbd1-30b8-83a1-89789387ee29> /Volumes/VOLUME/*/libobjc-trampolines.dylib
       0x10c18e000 -        0x10c1adfff org.cocoapods.AppCheckCore (10.19.2) <75e10be4-01bc-38d6-be72-bf1497254837> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/AppCheckCore.framework/AppCheckCore
       0x10c819000 -        0x10c8b4fff org.cocoapods.DKImagePickerController (4.3.9) <e7aa23fb-40c7-3ddb-82ff-1c27f22ca504> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/DKImagePickerController.framework/DKImagePickerController
       0x10c999000 -        0x10ca20fff org.cocoapods.DKPhotoGallery (0.0.19) <1ee82643-2f3f-3711-870a-3fcd63bd6746> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/DKPhotoGallery.framework/DKPhotoGallery
       0x10c699000 -        0x10c6acfff org.cocoapods.FBLPromises (2.4.0) <0a31bac6-b6cf-3a64-919b-b28e32a27beb> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FBLPromises.framework/FBLPromises
       0x10c15e000 -        0x10c169fff org.cocoapods.FirebaseABTesting (10.29.0) <3bb1b755-5544-3766-ae0d-817d43f83208> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseABTesting.framework/FirebaseABTesting
       0x10c6cd000 -        0x10c6d8fff org.cocoapods.FirebaseAppCheck (10.25.0) <b9861eb1-eb6f-3df8-84fb-dc17a55aad45> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseAppCheck.framework/FirebaseAppCheck
       0x10c1de000 -        0x10c1e1fff org.cocoapods.FirebaseAppCheckInterop (10.29.0) <1f8cbc29-619d-3ea1-9fea-fee8a564c3be> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseAppCheckInterop.framework/FirebaseAppCheckInterop
       0x10cad9000 -        0x10cb58fff org.cocoapods.FirebaseAuth (10.25.0) <4c9da416-d3da-39a1-abfe-f8849e8dac67> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseAuth.framework/FirebaseAuth
       0x10c1f2000 -        0x10c1f5fff org.cocoapods.FirebaseAuthInterop (10.29.0) <610ec85a-ddbb-36fd-8500-984a154c7fc6> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseAuthInterop.framework/FirebaseAuthInterop
       0x10c723000 -        0x10c736fff org.cocoapods.FirebaseCore (10.25.0) <09ac6880-65a6-3fc7-a583-45015de80fff> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseCore.framework/FirebaseCore
       0x10c6f1000 -        0x10c6f4fff org.cocoapods.FirebaseCoreExtension (10.29.0) <32ac2ee5-d42b-3ed9-a6ad-a64f8d0e0f75> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseCoreExtension.framework/FirebaseCoreExtension
       0x10cbed000 -        0x10cc10fff org.cocoapods.FirebaseCoreInternal (10.29.0) <add9505c-e64a-3e05-aff6-fd66b04dd928> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal
       0x10cd4d000 -        0x10cdbcfff org.cocoapods.FirebaseCrashlytics (10.25.0) <40080184-cc79-37bd-a60a-41d9c6acf2d8> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseCrashlytics.framework/FirebaseCrashlytics
       0x10cc5d000 -        0x10cc80fff org.cocoapods.FirebaseFirestore (10.25.0) <1c9f1206-f0e9-3420-8475-002b61dce148> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseFirestore.framework/FirebaseFirestore
       0x10e488000 -        0x10e9dbfff org.cocoapods.FirebaseFirestoreInternal (10.25.0) <9b643534-2a43-34ef-b743-d5c4a42f0895> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal
       0x10c796000 -        0x10c7b1fff org.cocoapods.FirebaseInstallations (10.29.0) <07bca220-514d-309c-9dae-4d373fc0bc1a> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseInstallations.framework/FirebaseInstallations
       0x10ce39000 -        0x10ce78fff org.cocoapods.FirebaseMessaging (10.25.0) <58d6d07e-8dc8-3a80-8f59-7b239a274965> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseMessaging.framework/FirebaseMessaging
       0x10ceb9000 -        0x10cef4fff org.cocoapods.FirebasePerformance (10.25.0) <3b9fc2bc-35a8-3ac6-8c34-1702102a0d05> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebasePerformance.framework/FirebasePerformance
       0x10cf39000 -        0x10cf74fff org.cocoapods.FirebaseRemoteConfig (10.29.0) <5736b99f-956a-3439-bab4-7b891c179043> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseRemoteConfig.framework/FirebaseRemoteConfig
       0x10c753000 -        0x10c75afff org.cocoapods.FirebaseRemoteConfigInterop (10.29.0) <9e59578b-212d-386e-abfe-d90d76f1d585> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseRemoteConfigInterop.framework/FirebaseRemoteConfigInterop
       0x10d03e000 -        0x10d06dfff org.cocoapods.FirebaseSessions (10.29.0) <c630b278-d778-3ff2-b19e-2729bb487a22> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseSessions.framework/FirebaseSessions
       0x10d17f000 -        0x10d1d2fff org.cocoapods.FirebaseSharedSwift (10.29.0) <8277e1c0-f9e2-3258-8216-08ad8bcc81b7> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseSharedSwift.framework/FirebaseSharedSwift
       0x10d31a000 -        0x10d37dfff org.cocoapods.FirebaseStorage (10.25.0) <f7728d28-fa96-3e26-bd8f-6f075722c755> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/FirebaseStorage.framework/FirebaseStorage
       0x10cfb5000 -        0x10cffcfff org.cocoapods.GTMSessionFetcher (2.3.0) <243ba06f-9dd6-3900-aa08-710d42942881> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/GTMSessionFetcher.framework/GTMSessionFetcher
       0x10d0ca000 -        0x10d0f9fff org.cocoapods.GoogleDataTransport (9.4.1) <d64665a1-3bbf-3dbc-bb9a-96185e7bb564> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/GoogleDataTransport.framework/GoogleDataTransport
       0x10ccc9000 -        0x10ccd4fff org.cocoapods.GoogleToolboxForMac (2.3.2) <375b7437-a632-38fe-b125-9fed666fe52f> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/GoogleToolboxForMac.framework/GoogleToolboxForMac
       0x10d12e000 -        0x10d151fff org.cocoapods.GoogleUtilities (7.13.3) <41bfdef8-1fa1-3c68-a8e1-06146c386f36> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/GoogleUtilities.framework/GoogleUtilities
       0x10cced000 -        0x10cd04fff org.cocoapods.Promises (2.4.0) <55e1260a-8ea0-34a5-ac91-e1ac0d1c081f> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/Promises.framework/Promises
       0x10d233000 -        0x10d23efff org.cocoapods.Reachability (5.2.4) <cd1c0764-6a2d-39eb-bc47-6c336ffc9e22> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/Reachability.framework/Reachability
       0x10c705000 -        0x10c708fff org.cocoapods.RecaptchaInterop (100.0.0) <e8dded0b-228e-3e2c-9c1a-dc70ee8cc3ce> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/RecaptchaInterop.framework/RecaptchaInterop
       0x10d4f3000 -        0x10d572fff org.cocoapods.SDWebImage (5.21.1) <3a9ac2db-52cb-389c-bf12-61173a34cb03> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/SDWebImage.framework/SDWebImage
       0x10d3fe000 -        0x10d429fff org.cocoapods.Stripe (24.7.0) <cbea4635-002f-3836-87fa-b4beaa0518d5> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/Stripe.framework/Stripe
       0x10d76c000 -        0x10d81bfff org.cocoapods.StripeApplePay (24.7.0) <3434dc76-92a0-3133-b4e1-2cf5fa1e2bd3> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/StripeApplePay.framework/StripeApplePay
       0x10db1d000 -        0x10dc04fff org.cocoapods.StripeCore (24.7.0) <1b531740-9ec6-38ac-863e-8d6ee6969861> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/StripeCore.framework/StripeCore
       0x1103b2000 -        0x1107d1fff org.cocoapods.StripeFinancialConnections (24.7.0) <86fb9ad6-87cb-3d47-bec3-0526c4232ceb> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/StripeFinancialConnections.framework/StripeFinancialConnections
       0x111812000 -        0x111d35fff org.cocoapods.StripePaymentSheet (24.7.0) <b425b88c-1492-3625-ba49-de63ee4e7bf3> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/StripePaymentSheet.framework/StripePaymentSheet
       0x10fad4000 -        0x10fdbffff org.cocoapods.StripePayments (24.7.0) <11ea70d4-afbe-3ba0-8f00-fa7ddc97e6ed> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/StripePayments.framework/StripePayments
       0x10dd49000 -        0x10de28fff org.cocoapods.StripePaymentsUI (24.7.0) <6ed0ee93-7098-335b-918f-f696f5768464> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/StripePaymentsUI.framework/StripePaymentsUI
       0x10e1ac000 -        0x10e297fff org.cocoapods.StripeUICore (24.7.0) <25f1ae51-9dc6-3792-a43d-c570feeedef7> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/StripeUICore.framework/StripeUICore
       0x10d2a8000 -        0x10d2bffff org.cocoapods.SwiftyGif (5.4.5) <a7ce88d6-ef15-3450-936e-7a31cf1cea07> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/SwiftyGif.framework/SwiftyGif
       0x10c773000 -        0x10c776fff org.cocoapods.Try (2.1.1) <30cf515b-6090-3139-a4b0-76802e4eba2e> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/Try.framework/Try
       0x110ff2000 -        0x111115fff org.cocoapods.absl (1.20240116.2) <5d2eddb1-ed92-3e20-90e9-1ee51a74ecdc> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/absl.framework/absl
       0x10c7d6000 -        0x10c7e1fff org.cocoapods.audio-session (0.0.1) <60514623-3e98-309b-8e55-61e5bab4812c> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/audio_session.framework/audio_session
       0x10c7f6000 -        0x10c7f9fff org.cocoapods.battery-plus (1.0.0) <63df91de-cc14-303e-8866-14d471eac9b6> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/battery_plus.framework/battery_plus
       0x10d684000 -        0x10d6c7fff org.cocoapods.camera-avfoundation (0.0.1) <6b384d41-26f2-3a95-8998-3421cb3743d8> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/camera_avfoundation.framework/camera_avfoundation
       0x10d2ec000 -        0x10d2f7fff org.cocoapods.connectivity-plus (0.0.1) <d1f90adc-4293-3476-9cc9-01191c7de5cb> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/connectivity_plus.framework/connectivity_plus
       0x10cd29000 -        0x10cd2cfff org.cocoapods.device-info-plus (0.0.1) <4d06832e-8776-328d-8563-20f4cb24993f> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/device_info_plus.framework/device_info_plus
       0x10c13b000 -        0x10c142fff org.cocoapods.disk-space (0.0.1) <9911bd3e-8855-34d6-a73f-234552395b4a> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/disk_space.framework/disk_space
       0x10d46a000 -        0x10d479fff org.cocoapods.file-picker (0.0.1) <60f1dc58-f5ea-3f3d-8f9b-ba35a79b4be7> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/file_picker.framework/file_picker
       0x10d492000 -        0x10d4a1fff org.cocoapods.flutter-local-notifications (0.0.1) <940d24ae-a309-316f-bd5d-d7d62a044166> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/flutter_local_notifications.framework/flutter_local_notifications
       0x10d27a000 -        0x10d27dfff org.cocoapods.flutter-native-splash (2.4.3) <681206d7-174d-3096-b0bd-b8f2aa74d53b> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/flutter_native_splash.framework/flutter_native_splash
       0x10d5e7000 -        0x10d5eefff org.cocoapods.flutter-pdfview (1.0.2) <80585b9d-8b6b-3808-9d10-a7d9fd7a11c4> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/flutter_pdfview.framework/flutter_pdfview
       0x10d603000 -        0x10d616fff org.cocoapods.flutter-secure-storage (6.0.0) <cc562d9a-c454-3ba2-ab47-9af124408377> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/flutter_secure_storage.framework/flutter_secure_storage
       0x10d637000 -        0x10d642fff org.cocoapods.geolocator-apple (1.2.0) <a29056e5-51a8-303c-905e-f7f2ea244e78> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/geolocator_apple.framework/geolocator_apple
       0x115250000 -        0x115e07fff org.cocoapods.grpc (1.62.5) <da065099-9e25-333a-8292-035a97174215> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/grpc.framework/grpc
       0x11238a000 -        0x112465fff org.cocoapods.grpcpp (1.62.5) <2c9653a3-535f-3b60-a344-9ff36b49ed14> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/grpcpp.framework/grpcpp
       0x10d720000 -        0x10d733fff org.cocoapods.image-picker-ios (0.0.1) <8c646ddb-6c2b-3f83-b97f-32fefc2e9eb6> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/image_picker_ios.framework/image_picker_ios
       0x10d4ba000 -        0x10d4bdfff org.cocoapods.integration-test (0.0.1) <1b75a54b-fd85-377a-bbee-8b11520e9b5f> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/integration_test.framework/integration_test
       0x10d92d000 -        0x10d944fff org.cocoapods.just-audio (0.0.1) <cd53970f-9393-39d7-9814-3c54ce0890da> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/just_audio.framework/just_audio
       0x10df69000 -        0x10dfd8fff org.cocoapods.leveldb (1.22.6) <55f342b2-5367-3ad2-8b6f-d63e5ded1fb3> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/leveldb.framework/leveldb
       0x10d8f8000 -        0x10d903fff org.cocoapods.local-auth-darwin (0.0.1) <859ca225-782f-3652-b81a-b14181e4d882> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/local_auth_darwin.framework/local_auth_darwin
       0x10d28e000 -        0x10d295fff org.cocoapods.location (0.0.1) <b7cf12a8-5203-3ef0-8c7c-0a1d9b5fa0c9> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/location.framework/location
       0x10d65b000 -        0x10d662fff org.cocoapods.nanopb (2.30910.0) <c19d7393-a7c2-3121-a9f6-5efadf8d7017> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/nanopb.framework/nanopb
       0x111332000 -        0x1114f9fff org.cocoapods.openssl-grpc (0.0.32) <5a6ebaee-476a-3d97-9ce5-051fa1da3bbf> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/openssl_grpc.framework/openssl_grpc
       0x10d4d2000 -        0x10d4d5fff org.cocoapods.package-info-plus (0.4.5) <299382c2-d806-31c9-a849-be6ee7c859a7> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/package_info_plus.framework/package_info_plus
       0x10d98c000 -        0x10d997fff org.cocoapods.path-provider-foundation (0.0.1) <1d21bc22-143c-3502-aac8-345c3207695b> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/path_provider_foundation.framework/path_provider_foundation
       0x10da18000 -        0x10da3ffff org.cocoapods.record-darwin (1.0.0) <a9086703-3f8f-32b1-acdf-71bb495a15b4> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/record_darwin.framework/record_darwin
       0x10d9e5000 -        0x10d9f4fff org.cocoapods.sensors-plus (0.0.1) <e1ea8ae8-e5a9-3e1e-9a92-909523c63f6f> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/sensors_plus.framework/sensors_plus
       0x10d750000 -        0x10d757fff org.cocoapods.share-plus (0.0.1) <e9337f50-0c6b-39d8-abf7-1f6308feafda> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/share_plus.framework/share_plus
       0x10dab2000 -        0x10dac5fff org.cocoapods.shared-preferences-foundation (0.0.1) <96e49fee-c3b2-315a-af3f-96038954a571> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation
       0x10d9b4000 -        0x10d9bbfff org.cocoapods.smart-auth (0.0.1) <571813f0-2466-3487-88fd-45dfbbdf8464> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/smart_auth.framework/smart_auth
       0x10e110000 -        0x10e133fff org.cocoapods.speech-to-text (0.0.1) <c338d03d-25d4-3366-bf33-cf5afcf18277> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/speech_to_text.framework/speech_to_text
       0x10e3f4000 -        0x10e413fff org.cocoapods.sqflite-darwin (0.0.4) <dd15ec20-c358-394d-87d8-80fe17f699e1> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/sqflite_darwin.framework/sqflite_darwin
       0x1127ae000 -        0x1128f5fff org.cocoapods.stripe-ios (0.0.1) <c1c2ab53-d208-30e8-a75c-b141de6f7521> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/stripe_ios.framework/stripe_ios
       0x10e438000 -        0x10e44bfff org.cocoapods.url-launcher-ios (0.0.1) <aac15d2d-77fe-388f-98e3-d10555d3319a> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/url_launcher_ios.framework/url_launcher_ios
       0x110cae000 -        0x110d71fff org.cocoapods.webview-flutter-wkwebview (0.0.1) <695a9978-2a59-3f96-91ec-ea97a797d1f1> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview
       0x1101a4000 -        0x1101bffff org.cocoapods.workmanager (0.0.1) <f21a1b5e-ca6d-3720-b305-d59ea39c8e00> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/workmanager.framework/workmanager
       0x11e30b000 -        0x1205d4fff io.flutter.flutter (1.0) <4c4c441b-5555-3144-a1b0-6645cf325e85> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Frameworks/Flutter.framework/Flutter
       0x10d961000 -        0x10d96cfff libsystem_platform.dylib (*) <9ae2c3dc-b88b-3c82-a0bd-918275fe7661> /usr/lib/system/libsystem_platform.dylib
       0x110e7e000 -        0x110eb9fff libsystem_kernel.dylib (*) <4b596cc5-f540-3f67-832e-1ec4159d2606> /usr/lib/system/libsystem_kernel.dylib
       0x10da78000 -        0x10da83fff libsystem_pthread.dylib (*) <51a43b06-feb4-3836-9e4f-21b69bb13726> /usr/lib/system/libsystem_pthread.dylib
       0x10daea000 -        0x10db03fff com.apple.mlcompiler.services (95) <7dcc25a8-4e35-3cca-a27d-8b9561e9d427> /Volumes/VOLUME/*/MLCompilerServices.framework/MLCompilerServices
       0x10c20b000 -        0x10c262fff dyld_sim (*) <39c2d7d4-1de8-3a61-a08f-81f004308f58> /Volumes/VOLUME/*/dyld_sim
       0x10734c000 -        0x10a19bfff com.example.cultureConnect (1.0.0) <c9708a29-e677-3d6e-944f-c43f98274bdb> /Users/<USER>/Library/Developer/CoreSimulator/Devices/9140C1FA-E7C6-41E3-8897-74037AD695D1/data/Containers/Bundle/Application/E327BAC5-D75E-493A-88D4-1A5475A8B9CD/Runner.app/Runner
    0x7ff8000d0000 -     0x7ff800154fff libsystem_c.dylib (*) <362f3aff-da5e-3175-81d6-8fbba4925a8a> /Volumes/VOLUME/*/libsystem_c.dylib
    0x7ff800287000 -     0x7ff80029bffb libc++abi.dylib (*) <f326cb12-f529-3daf-8ea4-b1d7a5b0ae7e> /Volumes/VOLUME/*/libc++abi.dylib
    0x7ff80003c000 -     0x7ff800076fd9 libobjc.A.dylib (*) <316eb0a5-f8ca-30f3-9c25-fc04f0aeff9e> /Volumes/VOLUME/*/libobjc.A.dylib
    0x7ff800155000 -     0x7ff80019fffd libdispatch.dylib (*) <20de26b2-4cb5-3346-98fd-e4488b4ea5bd> /Volumes/VOLUME/*/libdispatch.dylib
    0x7ff80036a000 -     0x7ff8006f8ff4 com.apple.CoreFoundation (6.9) <75d267fd-12bc-3192-b349-3e80ee673a5a> /Volumes/VOLUME/*/CoreFoundation.framework/CoreFoundation
    0x7ff80fe9a000 -     0x7ff80fea1ffe com.apple.GraphicsServices (1.0) <70698d67-e95b-3d0d-b12c-a6ad85767806> /Volumes/VOLUME/*/GraphicsServices.framework/GraphicsServices
    0x7ff804bbe000 -     0x7ff8069a0ffd com.apple.UIKitCore (1.0) <12622ad2-3713-30ef-b0eb-7cd0f7397650> /Volumes/VOLUME/*/UIKitCore.framework/UIKitCore
    0x7ff807989000 -     0x7ff8084cfff0 com.apple.Network (1.0) <5a88ef53-c626-34e9-95b6-e773e4c5fd30> /Volumes/VOLUME/*/Network.framework/Network
    0x7ff814d71000 -     0x7ff815229ffc libswiftCore.dylib (*) <5006b63c-6088-321c-9363-ce13d1cc9fc5> /Volumes/VOLUME/*/libswiftCore.dylib
    0x7ff800773000 -     0x7ff8012d6ff6 com.apple.Foundation (6.9) <30929a90-3f75-330c-ac6b-6a79bc83b134> /Volumes/VOLUME/*/Foundation.framework/Foundation
    0x7ff8000cc000 -     0x7ff8000cfff8 libsystem_blocks.dylib (*) <3ca9cb28-23b5-31fb-8214-3dd86b5f9a92> /Volumes/VOLUME/*/libsystem_blocks.dylib
               0x0 - 0xffffffffffffffff ??? (*) <00000000-0000-0000-0000-000000000000> ???
    0x7ff8046b1000 -     0x7ff804a58ffe com.apple.CFNetwork (1.0) <b5ac37ec-798b-3f44-a3fa-71f9f6a47d3c> /Volumes/VOLUME/*/CFNetwork.framework/CFNetwork
    0x7ff8134db000 -     0x7ff81359cff5 com.apple.TextInput (1.0) <1b7d33f5-88b6-367c-9797-341e8010417d> /Volumes/VOLUME/*/TextInput.framework/TextInput
    0x7ff8001a0000 -     0x7ff8001d8ff7 libsystem_malloc.dylib (*) <edf78275-cd92-3239-a406-38717fa2742d> /Volumes/VOLUME/*/libsystem_malloc.dylib
    0x7ff81522a000 -     0x7ff815483ffe com.apple.AssistantServices (1.0) <6b89eee7-a88c-35ff-9841-1290dfc8a2bc> /Volumes/VOLUME/*/AssistantServices.framework/AssistantServices

EOF

-----------
Full Report
-----------

{"app_name":"Runner","timestamp":"2025-07-02 15:34:54.00 +0100","app_version":"1.0.0","slice_uuid":"c9708a29-e677-3d6e-944f-c43f98274bdb","build_version":"1","platform":7,"bundleID":"com.example.cultureConnect","share_with_app_devs":0,"is_first_party":0,"bug_type":"309","os_version":"macOS 13.7.6 (22H625)","roots_installed":0,"name":"Runner","incident_id":"691612FD-AABF-42F9-B7A9-CA9E2A7F81CF"}
{
  "uptime" : 48000,
  "procRole" : "Foreground",
  "version" : 2,
  "userID" : 501,
  "deployVersion" : 210,
  "modelCode" : "MacBookPro14,3",
  "coalitionID" : 6601,
  "osVersion" : {
    "train" : "macOS 13.7.6",
    "build" : "22H625",
    "releaseType" : "User"
  },
  "captureTime" : "2025-07-02 15:33:06.4563 +0100",
  "incident" : "691612FD-AABF-42F9-B7A9-CA9E2A7F81CF",
  "pid" : 33720,
  "cpuType" : "X86-64",
  "roots_installed" : 0,
  "bug_type" : "309",
  "procLaunch" : "2025-07-02 15:30:22.3083 +0100",
  "procStartAbsTime" : 48578641061651,
  "procExitAbsTime" : 48741734765164,
  "procName" : "Runner",
  "procPath" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Runner",
  "bundleInfo" : {"CFBundleShortVersionString":"1.0.0","CFBundleVersion":"1","CFBundleIdentifier":"com.example.cultureConnect"},
  "storeInfo" : {"deviceIdentifierForVendor":"1DC5C248-9E4C-5A84-B71B-16456DC8321D","thirdParty":true},
  "parentProc" : "launchd_sim",
  "parentPid" : 7748,
  "coalitionName" : "com.apple.CoreSimulator.SimDevice.9140C1FA-E7C6-41E3-8897-74037AD695D1",
  "crashReporterKey" : "18C6C8E6-F6CA-43EA-C8A5-26AEBFD4BB5E",
  "responsiblePid" : 4183,
  "responsibleProc" : "SimulatorTrampoline",
  "codeSigningID" : "com.example.cultureConnect",
  "codeSigningTeamID" : "",
  "codeSigningFlags" : 570425861,
  "codeSigningValidationCategory" : 10,
  "codeSigningTrustLevel" : 0,
  "wakeTime" : 4548,
  "bridgeVersion" : {"build":"14Y910","train":"3.0"},
  "sleepWakeUUID" : "9841E335-C9AC-42D1-92B4-AC8E7B160038",
  "sip" : "enabled",
  "exception" : {"codes":"0x0000000000000000, 0x0000000000000000","rawCodes":[0,0],"type":"EXC_CRASH","signal":"SIGABRT"},
  "termination" : {"flags":0,"code":6,"namespace":"SIGNAL","indicator":"Abort trap: 6","byProc":"Runner","byPid":33720},
  "ktriageinfo" : "VM - (arg = 0x0) pmap_enter retried due to resource shortage\nVM - (arg = 0x0) pmap_enter retried due to resource shortage\nVM - (arg = 0x0) pmap_enter retried due to resource shortage\nVM - (arg = 0x0) pmap_enter retried due to resource shortage\nVM - (arg = 0x0) pmap_enter retried due to resource shortage\n",
  "extMods" : {"caller":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"system":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"targeted":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"warnings":0},
  "lastExceptionBacktrace" : [{"imageOffset":1251153,"symbol":"__exceptionPreprocess","symbolLocation":226,"imageIndex":94},{"imageOffset":162052,"symbol":"objc_exception_throw","symbolLocation":48,"imageIndex":92},{"imageOffset":1250879,"symbol":"-[NSException initWithCoder:]","symbolLocation":0,"imageIndex":94},{"imageOffset":1051447,"symbol":"+[GMSServices checkServicePreconditions]","symbolLocation":245,"imageIndex":89},{"imageOffset":1042798,"symbol":"+[GMSServices preLaunchServices]","symbolLocation":106,"imageIndex":89},{"imageOffset":38805763,"symbol":"-[FLTGoogleMapFactory sharedMapServices]","symbolLocation":51,"imageIndex":89},{"imageOffset":38805527,"symbol":"-[FLTGoogleMapFactory createWithFrame:viewIdentifier:arguments:]","symbolLocation":87,"imageIndex":89},{"imageOffset":321907,"symbol":"flutter::PlatformViewsController::OnCreate(FlutterMethodCall*, void (objc_object*) block_pointer)","symbolLocation":851,"imageIndex":83},{"imageOffset":320705,"symbol":"flutter::PlatformViewsController::OnMethodCall(FlutterMethodCall*, void (objc_object*) block_pointer)","symbolLocation":131,"imageIndex":83},{"imageOffset":6910301,"symbol":"__45-[FlutterMethodChannel setMethodCallHandler:]_block_invoke","symbolLocation":168,"imageIndex":83},{"imageOffset":385118,"symbol":"invocation function for block in flutter::PlatformMessageHandlerIos::HandlePlatformMessage(std::_fl::unique_ptr<flutter::PlatformMessage, std::_fl::default_delete<flutter::PlatformMessage>>)","symbolLocation":94,"imageIndex":83},{"imageOffset":6800,"symbol":"_dispatch_call_block_and_release","symbolLocation":12,"imageIndex":93},{"imageOffset":11578,"symbol":"_dispatch_client_callout","symbolLocation":8,"imageIndex":93},{"imageOffset":72384,"symbol":"_dispatch_main_queue_drain","symbolLocation":1420,"imageIndex":93},{"imageOffset":70950,"symbol":"_dispatch_main_queue_callback_4CF","symbolLocation":31,"imageIndex":93},{"imageOffset":581060,"symbol":"__CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__","symbolLocation":9,"imageIndex":94},{"imageOffset":558847,"symbol":"__CFRunLoopRun","symbolLocation":2463,"imageIndex":94},{"imageOffset":555389,"symbol":"CFRunLoopRunSpecific","symbolLocation":557,"imageIndex":94},{"imageOffset":12431,"symbol":"GSEventRunModal","symbolLocation":137,"imageIndex":95},{"imageOffset":16766269,"symbol":"-[UIApplication _run]","symbolLocation":972,"imageIndex":96},{"imageOffset":16785323,"symbol":"UIApplicationMain","symbolLocation":123,"imageIndex":96},{"imageOffset":601763,"imageIndex":96},{"imageOffset":27515,"sourceFile":"<compiler-generated>","symbol":"static UIApplicationDelegate.main()","symbolLocation":123,"imageIndex":89},{"imageOffset":27383,"sourceFile":"<compiler-generated>","symbol":"static AppDelegate.$main()","symbolLocation":39,"imageIndex":89},{"imageOffset":27624,"sourceLine":5,"sourceFile":"AppDelegate.swift","symbol":"main","imageIndex":89,"symbolLocation":24},{"imageOffset":5088,"symbol":"start_sim","symbolLocation":10,"imageIndex":88},{"imageOffset":25624,"symbol":"start","symbolLocation":1896,"imageIndex":0}],
  "faultingThread" : 0,
  "threads" : [{"triggered":true,"id":1711412,"threadState":{"r13":{"value":3},"rax":{"value":0},"rflags":{"value":582},"cpu":{"value":0},"r14":{"value":6},"rsi":{"value":6},"r8":{"value":0},"cr2":{"value":0},"rdx":{"value":0},"r10":{"value":0},"r9":{"value":10000000},"r15":{"value":22},"rbx":{"value":4749644416,"symbolLocation":0,"symbol":"_main_thread"},"trap":{"value":133},"err":{"value":33554760},"r11":{"value":582},"rip":{"value":4578632086,"matchesCrashFrame":1},"rbp":{"value":140701932916544},"rsp":{"value":140701932916504},"r12":{"value":259},"rcx":{"value":140701932916504},"flavor":"x86_THREAD_STATE","rdi":{"value":259}},"queue":"com.apple.main-thread","frames":[{"imageOffset":33174,"symbol":"__pthread_kill","symbolLocation":10,"imageIndex":85},{"imageOffset":24294,"symbol":"pthread_kill","symbolLocation":263,"imageIndex":86},{"imageOffset":511440,"symbol":"__abort","symbolLocation":145,"imageIndex":90},{"imageOffset":511295,"symbol":"abort","symbolLocation":148,"imageIndex":90},{"imageOffset":61874,"symbol":"abort_message","symbolLocation":241,"imageIndex":91},{"imageOffset":5002,"symbol":"demangling_terminate_handler()","symbolLocation":266,"imageIndex":91},{"imageOffset":26214,"symbol":"_objc_terminate()","symbolLocation":96,"imageIndex":92},{"imageOffset":92293,"sourceLine":463,"sourceFile":"FIRCLSException.mm","symbol":"FIRCLSTerminateHandler()","imageIndex":18,"symbolLocation":325},{"imageOffset":58891,"symbol":"std::__terminate(void (*)())","symbolLocation":6,"imageIndex":91},{"imageOffset":58822,"symbol":"std::terminate()","symbolLocation":54,"imageIndex":91},{"imageOffset":11598,"symbol":"_dispatch_client_callout","symbolLocation":28,"imageIndex":93},{"imageOffset":72384,"symbol":"_dispatch_main_queue_drain","symbolLocation":1420,"imageIndex":93},{"imageOffset":70950,"symbol":"_dispatch_main_queue_callback_4CF","symbolLocation":31,"imageIndex":93},{"imageOffset":581060,"symbol":"__CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__","symbolLocation":9,"imageIndex":94},{"imageOffset":558847,"symbol":"__CFRunLoopRun","symbolLocation":2463,"imageIndex":94},{"imageOffset":555389,"symbol":"CFRunLoopRunSpecific","symbolLocation":557,"imageIndex":94},{"imageOffset":12431,"symbol":"GSEventRunModal","symbolLocation":137,"imageIndex":95},{"imageOffset":16766269,"symbol":"-[UIApplication _run]","symbolLocation":972,"imageIndex":96},{"imageOffset":16785323,"symbol":"UIApplicationMain","symbolLocation":123,"imageIndex":96},{"imageOffset":601763,"imageIndex":96},{"imageOffset":27515,"sourceFile":"<compiler-generated>","symbol":"static UIApplicationDelegate.main()","symbolLocation":123,"imageIndex":89},{"imageOffset":27383,"sourceFile":"<compiler-generated>","symbol":"static AppDelegate.$main()","symbolLocation":39,"imageIndex":89},{"imageOffset":27624,"sourceLine":5,"sourceFile":"AppDelegate.swift","symbol":"main","imageIndex":89,"symbolLocation":24},{"imageOffset":5088,"symbol":"start_sim","symbolLocation":10,"imageIndex":88},{"imageOffset":25624,"symbol":"start","symbolLocation":1896,"imageIndex":0}]},{"id":1711911,"queue":"com.apple.root.background-qos","frames":[{"imageOffset":38840,"symbol":"DYLD-STUB$$NWPath.usesInterfaceType(_:)","symbolLocation":0,"imageIndex":51},{"imageOffset":15079,"sourceLine":15,"sourceFile":"PathMonitorConnectivityProvider.swift","symbol":"PathMonitorConnectivityProvider.currentConnectivityType.getter","imageIndex":51,"symbolLocation":455},{"imageOffset":17536,"sourceLine":58,"sourceFile":"PathMonitorConnectivityProvider.swift","symbol":"PathMonitorConnectivityProvider.pathUpdateHandler(path:)","imageIndex":51,"symbolLocation":160},{"imageOffset":17354,"sourceFile":"PathMonitorConnectivityProvider.swift","symbol":"implicit closure #2 in implicit closure #1 in PathMonitorConnectivityProvider.ensurePathMonitor()","symbolLocation":42,"imageIndex":51},{"imageOffset":205612,"symbol":"partial apply for closure #1 in NWPathMonitor.startLocked(lockedState:)","symbolLocation":44,"imageIndex":97},{"imageOffset":173113,"symbol":"thunk for @escaping @callee_guaranteed @Sendable () -> ()","symbolLocation":25,"imageIndex":97},{"imageOffset":6800,"symbol":"_dispatch_call_block_and_release","symbolLocation":12,"imageIndex":93},{"imageOffset":11578,"symbol":"_dispatch_client_callout","symbolLocation":8,"imageIndex":93},{"imageOffset":87938,"symbol":"_dispatch_root_queue_drain","symbolLocation":1240,"imageIndex":93},{"imageOffset":89736,"symbol":"_dispatch_worker_thread2","symbolLocation":244,"imageIndex":93},{"imageOffset":11279,"symbol":"_pthread_wqthread","symbolLocation":257,"imageIndex":86},{"imageOffset":7103,"symbol":"start_wqthread","symbolLocation":15,"imageIndex":86}]},{"id":1711913,"queue":"com.apple.root.background-qos","frames":[{"imageOffset":3509583,"symbol":"swift_conformsToProtocolMaybeInstantiateSuperclasses(swift::TargetMetadata<swift::InProcess> const*, swift::TargetProtocolDescriptor<swift::InProcess> const*, bool)","symbolLocation":3583,"imageIndex":98},{"imageOffset":3503454,"symbol":"swift_conformsToProtocolCommon","symbolLocation":78,"imageIndex":98},{"imageOffset":3174665,"symbol":"swift::_conformsToProtocol(swift::OpaqueValue const*, swift::TargetMetadata<swift::InProcess> const*, swift::TargetProtocolDescriptorRef<swift::InProcess>, swift::TargetWitnessTable<swift::InProcess> const**)","symbolLocation":41,"imageIndex":98},{"imageOffset":3498317,"symbol":"swift::_checkGenericRequirements(__swift::__runtime::llvm::ArrayRef<swift::TargetGenericRequirementDescriptor<swift::InProcess>>, __swift::__runtime::llvm::SmallVectorImpl<void const*>&, std::__1::function<void const* (unsigned int, unsigned int)>, std::__1::function<swift::TargetWitnessTable<swift::InProcess> const* (swift::TargetMetadata<swift::InProcess> const*, unsigned int)>)","symbolLocation":3981,"imageIndex":98},{"imageOffset":3416222,"symbol":"_gatherGenericParameters(swift::TargetContextDescriptor<swift::InProcess> const*, __swift::__runtime::llvm::ArrayRef<swift::MetadataOrPack>, swift::TargetMetadata<swift::InProcess> const*, __swift::__runtime::llvm::SmallVectorImpl<unsigned int>&, __swift::__runtime::llvm::SmallVectorImpl<void const*>&, swift::Demangle::__runtime::Demangler&)","symbolLocation":2750,"imageIndex":98},{"imageOffset":3453511,"symbol":"(anonymous namespace)::DecodedMetadataBuilder::createBoundGenericType(swift::TargetContextDescriptor<swift::InProcess> const*, __swift::__runtime::llvm::ArrayRef<swift::MetadataOrPack>, swift::MetadataOrPack) const","symbolLocation":199,"imageIndex":98},{"imageOffset":3447090,"symbol":"swift::Demangle::__runtime::TypeDecoder<(anonymous namespace)::DecodedMetadataBuilder>::decodeMangledType(swift::Demangle::__runtime::Node*, unsigned int, bool)","symbolLocation":13666,"imageIndex":98},{"imageOffset":3423912,"symbol":"swift_getTypeByMangledNodeImpl(swift::MetadataRequest, swift::Demangle::__runtime::Demangler&, swift::Demangle::__runtime::Node*, void const* const*, std::__1::function<void const* (unsigned int, unsigned int)>, std::__1::function<swift::TargetWitnessTable<swift::InProcess> const* (swift::TargetMetadata<swift::InProcess> const*, unsigned int)>)","symbolLocation":568,"imageIndex":98},{"imageOffset":3423259,"symbol":"swift_getTypeByMangledNode","symbolLocation":491,"imageIndex":98},{"imageOffset":3425308,"symbol":"swift_getTypeByMangledNameImpl(swift::MetadataRequest, __swift::__runtime::llvm::StringRef, void const* const*, std::__1::function<void const* (unsigned int, unsigned int)>, std::__1::function<swift::TargetWitnessTable<swift::InProcess> const* (swift::TargetMetadata<swift::InProcess> const*, unsigned int)>)","symbolLocation":1036,"imageIndex":98},{"imageOffset":3405755,"symbol":"swift_getTypeByMangledName","symbolLocation":491,"imageIndex":98},{"imageOffset":3406519,"symbol":"swift_getTypeByMangledNameInContextImpl(char const*, unsigned long, swift::TargetContextDescriptor<swift::InProcess> const*, void const* const*)","symbolLocation":183,"imageIndex":98},{"imageOffset":22023,"sourceFile":"<compiler-generated>","symbol":"__swift_instantiateConcreteTypeFromMangledName","symbolLocation":87,"imageIndex":26},{"imageOffset":135889,"sourceFile":"<compiler-generated>","symbol":"CacheKey.init(from:)","symbolLocation":113,"imageIndex":26},{"imageOffset":137183,"sourceFile":"<compiler-generated>","symbol":"protocol witness for Decodable.init(from:) in conformance CacheKey","symbolLocation":15,"imageIndex":26},{"imageOffset":2971623,"symbol":"dispatch thunk of Decodable.init(from:)","symbolLocation":7,"imageIndex":98},{"imageOffset":4775428,"symbol":"specialized JSONDecoderImpl.unwrap<A, B>(_:as:for:_:)","symbolLocation":996,"imageIndex":99},{"imageOffset":4886307,"symbol":"partial apply for closure #1 in JSONDecoder.decode<A>(_:from:)","symbolLocation":67,"imageIndex":99},{"imageOffset":4781678,"symbol":"closure #1 in JSONDecoder._decode<A>(_:from:)","symbolLocation":974,"imageIndex":99},{"imageOffset":4976313,"symbol":"partial apply for closure #1 in JSONDecoder._decode<A>(_:from:)","symbolLocation":25,"imageIndex":99},{"imageOffset":4782635,"symbol":"closure #1 in static JSONDecoder.withUTF8Representation<A>(of:_:)","symbolLocation":827,"imageIndex":99},{"imageOffset":4976345,"symbol":"partial apply for closure #1 in static JSONDecoder.withUTF8Representation<A>(of:_:)","symbolLocation":25,"imageIndex":99},{"imageOffset":3167398,"symbol":"closure #1 in Data.withBufferView<A>(_:)","symbolLocation":22,"imageIndex":99},{"imageOffset":4976373,"symbol":"partial apply for closure #1 in Data.withBufferView<A>(_:)","symbolLocation":21,"imageIndex":99},{"imageOffset":5001941,"symbol":"Data.InlineSlice.withUnsafeBytes<A>(_:)","symbolLocation":69,"imageIndex":99},{"imageOffset":4778818,"symbol":"JSONDecoder._decode<A>(_:from:)","symbolLocation":642,"imageIndex":99},{"imageOffset":4774416,"symbol":"JSONDecoder.decode<A>(_:from:)","symbolLocation":48,"imageIndex":99},{"imageOffset":4887951,"symbol":"dispatch thunk of JSONDecoder.decode<A>(_:from:)","symbolLocation":15,"imageIndex":99},{"imageOffset":139945,"sourceLine":74,"sourceFile":"SettingsCacheClient.swift","symbol":"SettingsCache.cacheKey.getter","imageIndex":26,"symbolLocation":745},{"imageOffset":143573,"sourceFile":"<compiler-generated>","symbol":"protocol witness for SettingsCacheClient.cacheKey.getter in conformance SettingsCache","symbolLocation":21,"imageIndex":26},{"imageOffset":78922,"sourceLine":113,"sourceFile":"RemoteSettings.swift","symbol":"RemoteSettings.isCacheExpired(time:)","imageIndex":26,"symbolLocation":778},{"imageOffset":77835,"sourceLine":56,"sourceFile":"RemoteSettings.swift","symbol":"RemoteSettings.fetchAndCacheSettings(currentTime:)","imageIndex":26,"symbolLocation":107},{"imageOffset":83749,"sourceLine":97,"sourceFile":"RemoteSettings.swift","symbol":"RemoteSettings.updateSettings(currentTime:)","imageIndex":26,"symbolLocation":37},{"imageOffset":82624,"sourceLine":101,"sourceFile":"RemoteSettings.swift","symbol":"RemoteSettings.updateSettings()","imageIndex":26,"symbolLocation":80},{"imageOffset":102305,"sourceLine":81,"sourceFile":"SessionsSettings.swift","symbol":"SessionsSettings.updateSettings()","imageIndex":26,"symbolLocation":81},{"imageOffset":102552,"sourceFile":"<compiler-generated>","symbol":"protocol witness for SettingsProtocol.updateSettings() in conformance SessionsSettings","symbolLocation":24,"imageIndex":26},{"imageOffset":40683,"sourceLine":193,"sourceFile":"FirebaseSessions.swift","symbol":"closure #1 in closure #1 in Sessions.init(appID:sessionGenerator:coordinator:initiator:appInfo:settings:loggedEventCallback:)","imageIndex":26,"symbolLocation":363},{"imageOffset":56003,"sourceFile":"<compiler-generated>","symbol":"partial apply for closure #1 in closure #1 in Sessions.init(appID:sessionGenerator:coordinator:initiator:appInfo:settings:loggedEventCallback:)","symbolLocation":35,"imageIndex":26},{"imageOffset":73756,"sourceLine":95,"sourceFile":"Promise+Then.swift","symbol":"closure #1 in Promise.then(on:_:)","imageIndex":33,"symbolLocation":348},{"imageOffset":70301,"sourceFile":"<compiler-generated>","symbol":"thunk for @escaping @callee_guaranteed (@guaranteed Swift.AnyObject?) -> (@out Any?)","symbolLocation":61,"imageIndex":33},{"imageOffset":16173,"sourceLine":273,"sourceFile":"FBLPromise.m","symbol":"__56-[FBLPromise chainOnQueue:chainedFulfill:chainedReject:]_block_invoke.63","imageIndex":9,"symbolLocation":77},{"imageOffset":13767,"sourceLine":226,"sourceFile":"FBLPromise.m","symbol":"__44-[FBLPromise observeOnQueue:fulfill:reject:]_block_invoke_2","imageIndex":9,"symbolLocation":103},{"imageOffset":6800,"symbol":"_dispatch_call_block_and_release","symbolLocation":12,"imageIndex":93},{"imageOffset":11578,"symbol":"_dispatch_client_callout","symbolLocation":8,"imageIndex":93},{"imageOffset":88103,"symbol":"_dispatch_root_queue_drain","symbolLocation":1405,"imageIndex":93},{"imageOffset":89736,"symbol":"_dispatch_worker_thread2","symbolLocation":244,"imageIndex":93},{"imageOffset":11279,"symbol":"_pthread_wqthread","symbolLocation":257,"imageIndex":86},{"imageOffset":7103,"symbol":"start_wqthread","symbolLocation":15,"imageIndex":86}]},{"id":1711920,"name":"com.apple.uikit.eventfetch-thread","frames":[{"imageOffset":5458,"symbol":"mach_msg2_trap","symbolLocation":10,"imageIndex":85},{"imageOffset":63181,"symbol":"mach_msg2_internal","symbolLocation":78,"imageIndex":85},{"imageOffset":34180,"symbol":"mach_msg_overwrite","symbolLocation":692,"imageIndex":85},{"imageOffset":6202,"symbol":"mach_msg","symbolLocation":19,"imageIndex":85},{"imageOffset":580403,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":143,"imageIndex":94},{"imageOffset":557755,"symbol":"__CFRunLoopRun","symbolLocation":1371,"imageIndex":94},{"imageOffset":555389,"symbol":"CFRunLoopRunSpecific","symbolLocation":557,"imageIndex":94},{"imageOffset":7371674,"symbol":"-[NSRunLoop(NSRunLoop) runMode:beforeDate:]","symbolLocation":213,"imageIndex":99},{"imageOffset":7372312,"symbol":"-[NSRunLoop(NSRunLoop) runUntilDate:]","symbolLocation":72,"imageIndex":99},{"imageOffset":17659395,"symbol":"-[UIEventFetcher threadMain]","symbolLocation":518,"imageIndex":96},{"imageOffset":7545669,"symbol":"__NSThread__start__","symbolLocation":1024,"imageIndex":99},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1711923,"queue":"GULLoggingClientQueue","frames":[{"imageOffset":16544,"sourceFile":"GULAppDelegateSwizzler.m","symbol":"__destroy_helper_block_e8_32s","symbolLocation":0,"imageIndex":32},{"imageOffset":4628,"symbol":"_call_dispose_helpers_excp","symbolLocation":45,"imageIndex":100},{"imageOffset":7845,"symbol":"_Block_release","symbolLocation":232,"imageIndex":100},{"imageOffset":11578,"symbol":"_dispatch_client_callout","symbolLocation":8,"imageIndex":93},{"imageOffset":43100,"symbol":"_dispatch_lane_serial_drain","symbolLocation":1236,"imageIndex":93},{"imageOffset":46071,"symbol":"_dispatch_lane_invoke","symbolLocation":406,"imageIndex":93},{"imageOffset":94124,"symbol":"_dispatch_root_queue_drain_deferred_wlh","symbolLocation":276,"imageIndex":93},{"imageOffset":91506,"symbol":"_dispatch_workloop_worker_thread","symbolLocation":552,"imageIndex":93},{"imageOffset":11349,"symbol":"_pthread_wqthread","symbolLocation":327,"imageIndex":86},{"imageOffset":7103,"symbol":"start_wqthread","symbolLocation":15,"imageIndex":86}]},{"id":1711929,"name":"io.flutter.1.ui","frames":[{"imageOffset":9010710,"symbol":"dart::ScavengerVisitorBase<true>::VisitPointers(dart::ObjectPtr*, dart::ObjectPtr*)","symbolLocation":86,"imageIndex":83},{"imageOffset":8319437,"symbol":"dart::UntaggedObject::VisitPointersPredefined(dart::ObjectPointerVisitor*, long)","symbolLocation":1005,"imageIndex":83},{"imageOffset":8992430,"symbol":"dart::ScavengerVisitorBase<true>::ProcessToSpace()","symbolLocation":94,"imageIndex":83},{"imageOffset":8987256,"symbol":"dart::ScavengerVisitorBase<true>::ProcessSurvivors()","symbolLocation":200,"imageIndex":83},{"imageOffset":8985941,"symbol":"dart::ParallelScavengerTask::RunEnteredIsolateGroup()","symbolLocation":133,"imageIndex":83},{"imageOffset":8967156,"symbol":"dart::SafepointTask::RunBlockedAtSafepoint()","symbolLocation":116,"imageIndex":83},{"imageOffset":8965770,"symbol":"dart::SafepointHandler::ExitSafepointLocked(dart::Thread*, dart::MonitorLocker*, dart::SafepointLevel)","symbolLocation":154,"imageIndex":83},{"imageOffset":8966983,"symbol":"dart::SafepointHandler::BlockForSafepoint(dart::Thread*)","symbolLocation":215,"imageIndex":83},{"imageOffset":9982125,"symbol":"dart::CompilerPass::Run(dart::CompilerPassState*) const","symbolLocation":621,"imageIndex":83},{"imageOffset":8826898,"symbol":"dart::CompileParsedFunctionHelper::Compile(dart::CompilationPipeline*)","symbolLocation":2002,"imageIndex":83},{"imageOffset":8829093,"symbol":"dart::CompileFunctionHelper(dart::CompilationPipeline*, dart::Function const&, bool, long)","symbolLocation":1125,"imageIndex":83},{"imageOffset":8827933,"symbol":"dart::Compiler::CompileFunction(dart::Thread*, dart::Function const&)","symbolLocation":253,"imageIndex":83},{"imageOffset":7899812,"symbol":"dart::Function::EnsureHasCodeNoThrow() const","symbolLocation":68,"imageIndex":83},{"imageOffset":7899645,"symbol":"dart::Function::EnsureHasCode() const","symbolLocation":61,"imageIndex":83},{"imageOffset":8822928,"symbol":"dart::DRT_CompileFunction(dart::NativeArguments)","symbolLocation":336,"imageIndex":83},{"imageOffset":**********,"imageIndex":101},{"imageOffset":**********,"imageIndex":101},{"imageOffset":**********,"imageIndex":101},{"imageOffset":**********,"imageIndex":101},{"imageOffset":**********,"imageIndex":101},{"imageOffset":**********,"imageIndex":101},{"imageOffset":**********,"imageIndex":101},{"imageOffset":**********,"imageIndex":101},{"imageOffset":**********,"imageIndex":101},{"imageOffset":**********,"imageIndex":101},{"imageOffset":5426232688,"imageIndex":101},{"imageOffset":5426231186,"imageIndex":101},{"imageOffset":5426230771,"imageIndex":101},{"imageOffset":5426230398,"imageIndex":101},{"imageOffset":5426230091,"imageIndex":101},{"imageOffset":5193351110,"imageIndex":101},{"imageOffset":5193406646,"imageIndex":101},{"imageOffset":5193406473,"imageIndex":101},{"imageOffset":5193453896,"imageIndex":101},{"imageOffset":5193437847,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193580468,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193585608,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193407050,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193580468,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5420726919,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5233690047,"imageIndex":101},{"imageOffset":5420717227,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5233669500,"imageIndex":101},{"imageOffset":5233662935,"imageIndex":101},{"imageOffset":5233651411,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193393463,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193406646,"imageIndex":101},{"imageOffset":5193406473,"imageIndex":101},{"imageOffset":5193453896,"imageIndex":101},{"imageOffset":5193437847,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193406646,"imageIndex":101},{"imageOffset":5193406473,"imageIndex":101},{"imageOffset":5193453896,"imageIndex":101},{"imageOffset":5193437847,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193580468,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5268540366,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193406646,"imageIndex":101},{"imageOffset":5193406473,"imageIndex":101},{"imageOffset":5193404116,"imageIndex":101},{"imageOffset":5193400672,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193406646,"imageIndex":101},{"imageOffset":5193406473,"imageIndex":101},{"imageOffset":5193404116,"imageIndex":101},{"imageOffset":5193400672,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193580468,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193354296,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193406646,"imageIndex":101},{"imageOffset":5193406473,"imageIndex":101},{"imageOffset":5193404116,"imageIndex":101},{"imageOffset":5193400672,"imageIndex":101},{"imageOffset":5193350520,"imageIndex":101},{"imageOffset":5193431820,"imageIndex":101},{"imageOffset":5193418442,"imageIndex":101},{"imageOffset":5193410743,"imageIndex":101},{"imageOffset":5193407687,"imageIndex":101},{"imageOffset":5193338459,"imageIndex":101},{"imageOffset":5148564788,"imageIndex":101},{"imageOffset":5148565555,"imageIndex":101},{"imageOffset":5148561605,"imageIndex":101},{"imageOffset":5260643209,"imageIndex":101},{"imageOffset":5260598641,"imageIndex":101},{"imageOffset":4853348944,"imageIndex":101},{"imageOffset":5260595503,"imageIndex":101},{"imageOffset":5271198337,"imageIndex":101},{"imageOffset":5271197673,"imageIndex":101},{"imageOffset":4853347654,"imageIndex":101},{"imageOffset":7370413,"symbol":"dart::DartEntry::InvokeFunction(dart::Function const&, dart::Array const&, dart::Array const&)","symbolLocation":365,"imageIndex":83},{"imageOffset":7371690,"symbol":"dart::DartEntry::InvokeCallable(dart::Thread*, dart::Function const&, dart::Array const&, dart::Array const&)","symbolLocation":266,"imageIndex":83},{"imageOffset":10845038,"symbol":"Dart_InvokeClosure","symbolLocation":1022,"imageIndex":83},{"imageOffset":6712757,"symbol":"tonic::DartInvokeVoid(_Dart_Handle*)","symbolLocation":15,"imageIndex":83},{"imageOffset":5731275,"symbol":"flutter::PlatformConfiguration::BeginFrame(fml::TimePoint, unsigned long long)","symbolLocation":655,"imageIndex":83},{"imageOffset":997013,"symbol":"flutter::RuntimeController::BeginFrame(fml::TimePoint, unsigned long long)","symbolLocation":61,"imageIndex":83},{"imageOffset":6723494,"symbol":"flutter::Animator::BeginFrame(std::_fl::unique_ptr<flutter::FrameTimingsRecorder, std::_fl::default_delete<flutter::FrameTimingsRecorder>>)","symbolLocation":554,"imageIndex":83},{"imageOffset":6736387,"symbol":"std::_fl::__function::__func<flutter::Animator::AwaitVSync()::$_0, std::_fl::allocator<flutter::Animator::AwaitVSync()::$_0>, void (std::_fl::unique_ptr<flutter::FrameTimingsRecorder, std::_fl::default_delete<flutter::FrameTimingsRecorder>>)>::operator()(std::_fl::unique_ptr<flutter::FrameTimingsRecorder, std::_fl::default_delete<flutter::FrameTimingsRecorder>>&&)","symbolLocation":69,"imageIndex":83},{"imageOffset":6886290,"symbol":"std::_fl::__function::__func<flutter::VsyncWaiter::FireCallback(fml::TimePoint, fml::TimePoint, bool)::$_0, std::_fl::allocator<flutter::VsyncWaiter::FireCallback(fml::TimePoint, fml::TimePoint, bool)::$_0>, void ()>::operator()()","symbolLocation":130,"imageIndex":83},{"imageOffset":521548,"symbol":"fml::MessageLoopImpl::FlushTasks(fml::FlushType)","symbolLocation":156,"imageIndex":83},{"imageOffset":547468,"symbol":"fml::MessageLoopDarwin::OnTimerFire(__CFRunLoopTimer*, fml::MessageLoopDarwin*)","symbolLocation":26,"imageIndex":83},{"imageOffset":584217,"symbol":"__CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__","symbolLocation":20,"imageIndex":94},{"imageOffset":583078,"symbol":"__CFRunLoopDoTimer","symbolLocation":801,"imageIndex":94},{"imageOffset":580906,"symbol":"__CFRunLoopDoTimers","symbolLocation":243,"imageIndex":94},{"imageOffset":558492,"symbol":"__CFRunLoopRun","symbolLocation":2108,"imageIndex":94},{"imageOffset":555389,"symbol":"CFRunLoopRunSpecific","symbolLocation":557,"imageIndex":94},{"imageOffset":547785,"symbol":"fml::MessageLoopDarwin::Run()","symbolLocation":65,"imageIndex":83},{"imageOffset":521322,"symbol":"fml::MessageLoopImpl::DoRun()","symbolLocation":22,"imageIndex":83},{"imageOffset":543353,"symbol":"std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()()","symbolLocation":135,"imageIndex":83},{"imageOffset":542747,"symbol":"fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*)","symbolLocation":27,"imageIndex":83},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1711930,"name":"io.flutter.1.raster","frames":[{"imageOffset":5458,"symbol":"mach_msg2_trap","symbolLocation":10,"imageIndex":85},{"imageOffset":63181,"symbol":"mach_msg2_internal","symbolLocation":78,"imageIndex":85},{"imageOffset":34180,"symbol":"mach_msg_overwrite","symbolLocation":692,"imageIndex":85},{"imageOffset":6202,"symbol":"mach_msg","symbolLocation":19,"imageIndex":85},{"imageOffset":580403,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":143,"imageIndex":94},{"imageOffset":557755,"symbol":"__CFRunLoopRun","symbolLocation":1371,"imageIndex":94},{"imageOffset":555389,"symbol":"CFRunLoopRunSpecific","symbolLocation":557,"imageIndex":94},{"imageOffset":547785,"symbol":"fml::MessageLoopDarwin::Run()","symbolLocation":65,"imageIndex":83},{"imageOffset":521322,"symbol":"fml::MessageLoopImpl::DoRun()","symbolLocation":22,"imageIndex":83},{"imageOffset":543353,"symbol":"std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()()","symbolLocation":135,"imageIndex":83},{"imageOffset":542747,"symbol":"fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*)","symbolLocation":27,"imageIndex":83},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1711931,"name":"io.flutter.1.io","frames":[{"imageOffset":5458,"symbol":"mach_msg2_trap","symbolLocation":10,"imageIndex":85},{"imageOffset":63181,"symbol":"mach_msg2_internal","symbolLocation":78,"imageIndex":85},{"imageOffset":34180,"symbol":"mach_msg_overwrite","symbolLocation":692,"imageIndex":85},{"imageOffset":6202,"symbol":"mach_msg","symbolLocation":19,"imageIndex":85},{"imageOffset":580403,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":143,"imageIndex":94},{"imageOffset":557755,"symbol":"__CFRunLoopRun","symbolLocation":1371,"imageIndex":94},{"imageOffset":555389,"symbol":"CFRunLoopRunSpecific","symbolLocation":557,"imageIndex":94},{"imageOffset":547785,"symbol":"fml::MessageLoopDarwin::Run()","symbolLocation":65,"imageIndex":83},{"imageOffset":521322,"symbol":"fml::MessageLoopImpl::DoRun()","symbolLocation":22,"imageIndex":83},{"imageOffset":543353,"symbol":"std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()()","symbolLocation":135,"imageIndex":83},{"imageOffset":542747,"symbol":"fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*)","symbolLocation":27,"imageIndex":83},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1711932,"name":"io.flutter.1.profiler","frames":[{"imageOffset":5458,"symbol":"mach_msg2_trap","symbolLocation":10,"imageIndex":85},{"imageOffset":63181,"symbol":"mach_msg2_internal","symbolLocation":78,"imageIndex":85},{"imageOffset":34180,"symbol":"mach_msg_overwrite","symbolLocation":692,"imageIndex":85},{"imageOffset":6202,"symbol":"mach_msg","symbolLocation":19,"imageIndex":85},{"imageOffset":580403,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":143,"imageIndex":94},{"imageOffset":557755,"symbol":"__CFRunLoopRun","symbolLocation":1371,"imageIndex":94},{"imageOffset":555389,"symbol":"CFRunLoopRunSpecific","symbolLocation":557,"imageIndex":94},{"imageOffset":547785,"symbol":"fml::MessageLoopDarwin::Run()","symbolLocation":65,"imageIndex":83},{"imageOffset":521322,"symbol":"fml::MessageLoopImpl::DoRun()","symbolLocation":22,"imageIndex":83},{"imageOffset":543353,"symbol":"std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()()","symbolLocation":135,"imageIndex":83},{"imageOffset":542747,"symbol":"fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*)","symbolLocation":27,"imageIndex":83},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1711933,"name":"io.worker.1","frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":391104,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":18,"imageIndex":83},{"imageOffset":506857,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":167,"imageIndex":83},{"imageOffset":509195,"symbol":"void* std::_fl::__thread_proxy[abi:v15000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":191,"imageIndex":83},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1711934,"name":"io.worker.2","frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":391104,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":18,"imageIndex":83},{"imageOffset":506857,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":167,"imageIndex":83},{"imageOffset":509195,"symbol":"void* std::_fl::__thread_proxy[abi:v15000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":191,"imageIndex":83},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1711935,"name":"io.worker.3","frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":391104,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":18,"imageIndex":83},{"imageOffset":506857,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":167,"imageIndex":83},{"imageOffset":509195,"symbol":"void* std::_fl::__thread_proxy[abi:v15000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":191,"imageIndex":83},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1711936,"name":"io.worker.4","frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":391104,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":18,"imageIndex":83},{"imageOffset":506857,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":167,"imageIndex":83},{"imageOffset":509195,"symbol":"void* std::_fl::__thread_proxy[abi:v15000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":191,"imageIndex":83},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1711937,"name":"dart:io EventHandler","frames":[{"imageOffset":24970,"symbol":"kevent","symbolLocation":10,"imageIndex":85},{"imageOffset":6572296,"symbol":"dart::bin::EventHandlerImplementation::EventHandlerEntry(unsigned long)","symbolLocation":312,"imageIndex":83},{"imageOffset":6687603,"symbol":"dart::bin::ThreadStart(void*)","symbolLocation":83,"imageIndex":83},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1711939,"name":"Dart Profiler ThreadInterrupter","frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26509,"symbol":"_pthread_cond_wait","symbolLocation":1295,"imageIndex":86},{"imageOffset":7073774,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":126,"imageIndex":83},{"imageOffset":8749858,"symbol":"dart::ThreadInterrupter::ThreadMain(unsigned long)","symbolLocation":322,"imageIndex":83},{"imageOffset":8261774,"symbol":"dart::ThreadStart(void*)","symbolLocation":206,"imageIndex":83},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1711940,"name":"Dart Profiler SampleBlockProcessor","frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26509,"symbol":"_pthread_cond_wait","symbolLocation":1295,"imageIndex":86},{"imageOffset":7073774,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":126,"imageIndex":83},{"imageOffset":8282750,"symbol":"dart::SampleBlockProcessor::ThreadMain(unsigned long)","symbolLocation":270,"imageIndex":83},{"imageOffset":8261774,"symbol":"dart::ThreadStart(void*)","symbolLocation":206,"imageIndex":83},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1711960,"name":"DartWorker","frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26509,"symbol":"_pthread_cond_wait","symbolLocation":1295,"imageIndex":86},{"imageOffset":7073774,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":126,"imageIndex":83},{"imageOffset":7481757,"symbol":"dart::MutatorThreadPool::OnEnterIdleLocked(dart::MutexLocker*, dart::ThreadPool::Worker*)","symbolLocation":253,"imageIndex":83},{"imageOffset":8753152,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":112,"imageIndex":83},{"imageOffset":8753971,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":115,"imageIndex":83},{"imageOffset":8261774,"symbol":"dart::ThreadStart(void*)","symbolLocation":206,"imageIndex":83},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1712142,"name":"com.apple.NSURLConnectionLoader","frames":[{"imageOffset":5458,"symbol":"mach_msg2_trap","symbolLocation":10,"imageIndex":85},{"imageOffset":63181,"symbol":"mach_msg2_internal","symbolLocation":78,"imageIndex":85},{"imageOffset":34180,"symbol":"mach_msg_overwrite","symbolLocation":692,"imageIndex":85},{"imageOffset":6202,"symbol":"mach_msg","symbolLocation":19,"imageIndex":85},{"imageOffset":580403,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":143,"imageIndex":94},{"imageOffset":557755,"symbol":"__CFRunLoopRun","symbolLocation":1371,"imageIndex":94},{"imageOffset":555389,"symbol":"CFRunLoopRunSpecific","symbolLocation":557,"imageIndex":94},{"imageOffset":2318411,"imageIndex":102},{"imageOffset":7545669,"symbol":"__NSThread__start__","symbolLocation":1024,"imageIndex":99},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1712181,"queue":"APMAnalyticsQueue","frames":[{"imageOffset":11470,"symbol":"__ulock_wait","symbolLocation":10,"imageIndex":85},{"imageOffset":6919,"symbol":"_os_unfair_lock_lock_slow","symbolLocation":162,"imageIndex":84},{"imageOffset":107069,"symbol":"lookUpImpOrForward","symbolLocation":92,"imageIndex":92},{"imageOffset":9883,"symbol":"_objc_msgSend_uncached","symbolLocation":75,"imageIndex":92},{"imageOffset":79127,"symbol":"__65+[APMAnalytics logEventWithOrigin:isPublicEvent:name:parameters:]_block_invoke_3","symbolLocation":59,"imageIndex":89},{"imageOffset":6800,"symbol":"_dispatch_call_block_and_release","symbolLocation":12,"imageIndex":93},{"imageOffset":11578,"symbol":"_dispatch_client_callout","symbolLocation":8,"imageIndex":93},{"imageOffset":43100,"symbol":"_dispatch_lane_serial_drain","symbolLocation":1236,"imageIndex":93},{"imageOffset":46071,"symbol":"_dispatch_lane_invoke","symbolLocation":406,"imageIndex":93},{"imageOffset":94124,"symbol":"_dispatch_root_queue_drain_deferred_wlh","symbolLocation":276,"imageIndex":93},{"imageOffset":91506,"symbol":"_dispatch_workloop_worker_thread","symbolLocation":552,"imageIndex":93},{"imageOffset":11349,"symbol":"_pthread_wqthread","symbolLocation":327,"imageIndex":86},{"imageOffset":7103,"symbol":"start_wqthread","symbolLocation":15,"imageIndex":86}]},{"id":1712230,"name":"com.google.firebase.crashlytics.MachExceptionServer","frames":[{"imageOffset":5458,"symbol":"mach_msg2_trap","symbolLocation":10,"imageIndex":85},{"imageOffset":63181,"symbol":"mach_msg2_internal","symbolLocation":78,"imageIndex":85},{"imageOffset":34180,"symbol":"mach_msg_overwrite","symbolLocation":692,"imageIndex":85},{"imageOffset":6202,"symbol":"mach_msg","symbolLocation":19,"imageIndex":85},{"imageOffset":161166,"sourceLine":192,"sourceFile":"FIRCLSMachException.c","symbol":"FIRCLSMachExceptionReadMessage","imageIndex":18,"symbolLocation":78},{"imageOffset":160976,"sourceLine":168,"sourceFile":"FIRCLSMachException.c","symbol":"FIRCLSMachExceptionServer","imageIndex":18,"symbolLocation":48},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1715004,"queue":"APMExperimentWorkerQueue","frames":[{"imageOffset":9904,"symbol":"_objc_msgSend_uncached","symbolLocation":96,"imageIndex":92},{"imageOffset":227828,"symbol":"-[APMEDatabase initializeDatabaseResourcesWithContext:databasePath:]","symbolLocation":59,"imageIndex":89},{"imageOffset":227636,"symbol":"-[APMEDatabase initWithPath:]","symbolLocation":147,"imageIndex":89},{"imageOffset":266075,"symbol":"-[APMETaskManager startTaskManagerOnWorkerQueue]","symbolLocation":73,"imageIndex":89},{"imageOffset":265987,"symbol":"__35-[APMETaskManager startTaskManager]_block_invoke","symbolLocation":34,"imageIndex":89},{"imageOffset":271969,"symbol":"__46-[APMETaskManager dispatchAsyncOnWorkerQueue:]_block_invoke","symbolLocation":28,"imageIndex":89},{"imageOffset":6800,"symbol":"_dispatch_call_block_and_release","symbolLocation":12,"imageIndex":93},{"imageOffset":11578,"symbol":"_dispatch_client_callout","symbolLocation":8,"imageIndex":93},{"imageOffset":43100,"symbol":"_dispatch_lane_serial_drain","symbolLocation":1236,"imageIndex":93},{"imageOffset":46071,"symbol":"_dispatch_lane_invoke","symbolLocation":406,"imageIndex":93},{"imageOffset":94124,"symbol":"_dispatch_root_queue_drain_deferred_wlh","symbolLocation":276,"imageIndex":93},{"imageOffset":91506,"symbol":"_dispatch_workloop_worker_thread","symbolLocation":552,"imageIndex":93},{"imageOffset":11349,"symbol":"_pthread_wqthread","symbolLocation":327,"imageIndex":86},{"imageOffset":7103,"symbol":"start_wqthread","symbolLocation":15,"imageIndex":86}]},{"id":1715239,"name":"DartWorker","frames":[{"imageOffset":9011017,"symbol":"dart::ScavengerVisitorBase<true>::VisitPointers(dart::ObjectPtr*, dart::ObjectPtr*)","symbolLocation":393,"imageIndex":83},{"imageOffset":8992825,"symbol":"dart::ScavengerVisitorBase<true>::ProcessToSpace()","symbolLocation":489,"imageIndex":83},{"imageOffset":8987256,"symbol":"dart::ScavengerVisitorBase<true>::ProcessSurvivors()","symbolLocation":200,"imageIndex":83},{"imageOffset":8985941,"symbol":"dart::ParallelScavengerTask::RunEnteredIsolateGroup()","symbolLocation":133,"imageIndex":83},{"imageOffset":8967656,"symbol":"dart::SafepointHandler::RunTasks(dart::IntrusiveDList<dart::SafepointTask, 1>*)","symbolLocation":376,"imageIndex":83},{"imageOffset":8979845,"symbol":"dart::Scavenger::ParallelScavenge(dart::SemiSpace*)","symbolLocation":485,"imageIndex":83},{"imageOffset":8978678,"symbol":"dart::Scavenger::Scavenge(dart::Thread*, dart::GCType, dart::GCReason)","symbolLocation":406,"imageIndex":83},{"imageOffset":8894414,"symbol":"dart::Heap::CollectNewSpaceGarbage(dart::Thread*, dart::GCType, dart::GCReason)","symbolLocation":510,"imageIndex":83},{"imageOffset":8889160,"symbol":"dart::Heap::AllocateNew(dart::Thread*, long)","symbolLocation":360,"imageIndex":83},{"imageOffset":7797994,"symbol":"dart::Object::Allocate(long, long, dart::Heap::Space, bool, unsigned long, unsigned long)","symbolLocation":74,"imageIndex":83},{"imageOffset":8015256,"symbol":"dart::String::FromUTF8(unsigned char const*, long, dart::Heap::Space)","symbolLocation":280,"imageIndex":83},{"imageOffset":10262016,"symbol":"dart::kernel::TranslationHelper::DartString(dart::kernel::StringIndex, dart::Heap::Space)","symbolLocation":176,"imageIndex":83},{"imageOffset":10263284,"symbol":"dart::kernel::TranslationHelper::DartFactoryName(dart::kernel::NameIndex)","symbolLocation":244,"imageIndex":83},{"imageOffset":10266270,"symbol":"dart::kernel::TranslationHelper::LookupStaticMethodByKernelProcedure(dart::kernel::NameIndex, bool)","symbolLocation":30,"imageIndex":83},{"imageOffset":10106480,"symbol":"dart::kernel::StreamingFlowGraphBuilder::BuildStaticInvocation(dart::TokenPosition*)","symbolLocation":144,"imageIndex":83},{"imageOffset":10131621,"symbol":"dart::kernel::StreamingFlowGraphBuilder::BuildVariableDeclaration(dart::TokenPosition*)","symbolLocation":181,"imageIndex":83},{"imageOffset":10119898,"symbol":"dart::kernel::StreamingFlowGraphBuilder::BuildBlock(dart::TokenPosition*)","symbolLocation":170,"imageIndex":83},{"imageOffset":10089630,"symbol":"dart::kernel::StreamingFlowGraphBuilder::BuildFunctionBody(dart::Function const&, dart::LocalVariable*, bool)","symbolLocation":478,"imageIndex":83},{"imageOffset":10091179,"symbol":"dart::kernel::StreamingFlowGraphBuilder::BuildGraphOfFunction(bool)","symbolLocation":651,"imageIndex":83},{"imageOffset":10092231,"symbol":"dart::kernel::StreamingFlowGraphBuilder::BuildGraph()","symbolLocation":311,"imageIndex":83},{"imageOffset":10167141,"symbol":"dart::kernel::FlowGraphBuilder::BuildGraph()","symbolLocation":101,"imageIndex":83},{"imageOffset":8821998,"symbol":"dart::DartCompilationPipeline::BuildFlowGraph(dart::Zone*, dart::ParsedFunction*, dart::ZoneGrowableArray<dart::ICData const*>*, long, bool)","symbolLocation":62,"imageIndex":83},{"imageOffset":8826365,"symbol":"dart::CompileParsedFunctionHelper::Compile(dart::CompilationPipeline*)","symbolLocation":1469,"imageIndex":83},{"imageOffset":8829093,"symbol":"dart::CompileFunctionHelper(dart::CompilationPipeline*, dart::Function const&, bool, long)","symbolLocation":1125,"imageIndex":83},{"imageOffset":8830823,"symbol":"dart::Compiler::CompileOptimizedFunction(dart::Thread*, dart::Function const&, long)","symbolLocation":263,"imageIndex":83},{"imageOffset":8832362,"symbol":"dart::BackgroundCompiler::Run()","symbolLocation":266,"imageIndex":83},{"imageOffset":8753334,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":294,"imageIndex":83},{"imageOffset":8753971,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":115,"imageIndex":83},{"imageOffset":8261774,"symbol":"dart::ThreadStart(void*)","symbolLocation":206,"imageIndex":83},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1715530,"frames":[{"imageOffset":7088,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":86}]},{"id":1715531,"queue":"com.apple.UIKit.UITextInputSessionActionAnalytics","frames":[{"imageOffset":11470,"symbol":"__ulock_wait","symbolLocation":10,"imageIndex":85},{"imageOffset":6919,"symbol":"_os_unfair_lock_lock_slow","symbolLocation":162,"imageIndex":84},{"imageOffset":107069,"symbol":"lookUpImpOrForward","symbolLocation":92,"imageIndex":92},{"imageOffset":9883,"symbol":"_objc_msgSend_uncached","symbolLocation":75,"imageIndex":92},{"imageOffset":336068,"symbol":"-[TIAnalyticsService dispatchEventWithName:values:inputMode:]","symbolLocation":46,"imageIndex":103},{"imageOffset":3794410,"symbol":"__49+[UIKBAnalyticsDispatcher sessionAnalyticsEnded:]_block_invoke.152","symbolLocation":355,"imageIndex":96},{"imageOffset":20631060,"symbol":"-[_UITextInputSessionAccumulator enumerateAnalytics:]","symbolLocation":302,"imageIndex":96},{"imageOffset":300324,"symbol":"__NSSET_IS_CALLING_OUT_TO_A_BLOCK__","symbolLocation":7,"imageIndex":94},{"imageOffset":1368776,"symbol":"-[__NSSetM enumerateObjectsWithOptions:usingBlock:]","symbolLocation":237,"imageIndex":94},{"imageOffset":10723550,"symbol":"__56-[UITextInputSessionActionAnalytics enumerateAnalytics:]_block_invoke","symbolLocation":124,"imageIndex":96},{"imageOffset":6800,"symbol":"_dispatch_call_block_and_release","symbolLocation":12,"imageIndex":93},{"imageOffset":11578,"symbol":"_dispatch_client_callout","symbolLocation":8,"imageIndex":93},{"imageOffset":43100,"symbol":"_dispatch_lane_serial_drain","symbolLocation":1236,"imageIndex":93},{"imageOffset":46071,"symbol":"_dispatch_lane_invoke","symbolLocation":406,"imageIndex":93},{"imageOffset":94124,"symbol":"_dispatch_root_queue_drain_deferred_wlh","symbolLocation":276,"imageIndex":93},{"imageOffset":91506,"symbol":"_dispatch_workloop_worker_thread","symbolLocation":552,"imageIndex":93},{"imageOffset":11349,"symbol":"_pthread_wqthread","symbolLocation":327,"imageIndex":86},{"imageOffset":7103,"symbol":"start_wqthread","symbolLocation":15,"imageIndex":86}]},{"id":1715532,"frames":[{"imageOffset":7088,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":86}]},{"id":1715533,"queue":"com.google.fira.worker","frames":[{"imageOffset":368635,"symbol":"-[APMMeasurement setEnabledOnWorkerQueue:]","symbolLocation":34,"imageIndex":89},{"imageOffset":368588,"symbol":"__29-[APMMeasurement setEnabled:]_block_invoke","symbolLocation":42,"imageIndex":89},{"imageOffset":798613,"symbol":"__51-[APMScheduler scheduleOnWorkerQueueBlockID:block:]_block_invoke","symbolLocation":29,"imageIndex":89},{"imageOffset":6800,"symbol":"_dispatch_call_block_and_release","symbolLocation":12,"imageIndex":93},{"imageOffset":11578,"symbol":"_dispatch_client_callout","symbolLocation":8,"imageIndex":93},{"imageOffset":43100,"symbol":"_dispatch_lane_serial_drain","symbolLocation":1236,"imageIndex":93},{"imageOffset":46071,"symbol":"_dispatch_lane_invoke","symbolLocation":406,"imageIndex":93},{"imageOffset":94124,"symbol":"_dispatch_root_queue_drain_deferred_wlh","symbolLocation":276,"imageIndex":93},{"imageOffset":91506,"symbol":"_dispatch_workloop_worker_thread","symbolLocation":552,"imageIndex":93},{"imageOffset":11349,"symbol":"_pthread_wqthread","symbolLocation":327,"imageIndex":86},{"imageOffset":7103,"symbol":"start_wqthread","symbolLocation":15,"imageIndex":86}]},{"id":1715534,"frames":[{"imageOffset":7088,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":86}]},{"id":1716141,"queue":"APMMonitorQueue","frames":[{"imageOffset":125016,"symbol":"free","symbolLocation":0,"imageIndex":104},{"imageOffset":288645,"symbol":"__vfprintf","symbolLocation":16363,"imageIndex":90},{"imageOffset":327526,"symbol":"_vsnprintf","symbolLocation":330,"imageIndex":90},{"imageOffset":327617,"symbol":"vsnprintf_l","symbolLocation":41,"imageIndex":90},{"imageOffset":267007,"symbol":"snprintf_l","symbolLocation":123,"imageIndex":90},{"imageOffset":742442,"symbol":"__CFStringAppendFormatCore","symbolLocation":12203,"imageIndex":94},{"imageOffset":747778,"symbol":"_CFStringCreateWithFormatAndArgumentsReturningMetadata","symbolLocation":170,"imageIndex":94},{"imageOffset":747602,"symbol":"_CFStringCreateWithFormatAndArgumentsAux2","symbolLocation":28,"imageIndex":94},{"imageOffset":7491624,"symbol":"-[NSPlaceholderString initWithFormat:locale:arguments:]","symbolLocation":188,"imageIndex":99},{"imageOffset":7460451,"symbol":"+[NSString stringWithFormat:]","symbolLocation":160,"imageIndex":99},{"imageOffset":91145,"symbol":"-[APMASLLogger logMessage:logTag:messageCode:withLogLevel:]","symbolLocation":137,"imageIndex":89},{"imageOffset":511243,"symbol":"__44-[APMMonitor logToConsole:message:logLevel:]_block_invoke","symbolLocation":103,"imageIndex":89},{"imageOffset":6800,"symbol":"_dispatch_call_block_and_release","symbolLocation":12,"imageIndex":93},{"imageOffset":11578,"symbol":"_dispatch_client_callout","symbolLocation":8,"imageIndex":93},{"imageOffset":43100,"symbol":"_dispatch_lane_serial_drain","symbolLocation":1236,"imageIndex":93},{"imageOffset":46071,"symbol":"_dispatch_lane_invoke","symbolLocation":406,"imageIndex":93},{"imageOffset":94124,"symbol":"_dispatch_root_queue_drain_deferred_wlh","symbolLocation":276,"imageIndex":93},{"imageOffset":91506,"symbol":"_dispatch_workloop_worker_thread","symbolLocation":552,"imageIndex":93},{"imageOffset":11349,"symbol":"_pthread_wqthread","symbolLocation":327,"imageIndex":86},{"imageOffset":7103,"symbol":"start_wqthread","symbolLocation":15,"imageIndex":86}]},{"id":1716142,"queue":"com.google.firebase.firestore.rpc","frames":[{"imageOffset":33346,"symbol":"poll","symbolLocation":10,"imageIndex":85},{"imageOffset":3521056,"sourceLine":1020,"sourceFile":"ev_poll_posix.cc","symbol":"pollset_work(grpc_pollset*, grpc_pollset_worker**, grpc_core::Timestamp)","imageIndex":60,"symbolLocation":1984},{"imageOffset":3538815,"sourceLine":249,"sourceFile":"ev_posix.cc","symbol":"pollset_work(grpc_pollset*, grpc_pollset_worker**, grpc_core::Timestamp)","imageIndex":60,"symbolLocation":159},{"imageOffset":6006592,"sourceLine":48,"sourceFile":"pollset.cc","symbol":"grpc_pollset_work(grpc_pollset*, grpc_pollset_worker**, grpc_core::Timestamp)","imageIndex":60,"symbolLocation":64},{"imageOffset":2777167,"sourceLine":1043,"sourceFile":"completion_queue.cc","symbol":"cq_next(grpc_completion_queue*, gpr_timespec, void*)","imageIndex":60,"symbolLocation":975},{"imageOffset":2773901,"sourceLine":1121,"sourceFile":"completion_queue.cc","symbol":"grpc_completion_queue_next","imageIndex":60,"symbolLocation":61},{"imageOffset":212533,"sourceLine":146,"sourceFile":"completion_queue_cc.cc","symbol":"grpc::CompletionQueue::AsyncNextInternal(void**, bool*, gpr_timespec)","imageIndex":61,"symbolLocation":69},{"imageOffset":558093,"sourceLine":182,"sourceFile":"completion_queue.h","symbol":"grpc::CompletionQueue::Next(void**, bool*)","imageIndex":20,"symbolLocation":93},{"imageOffset":557827,"sourceLine":143,"sourceFile":"datastore.cc","symbol":"firebase::firestore::remote::Datastore::PollGrpcQueue()","imageIndex":20,"symbolLocation":179},{"imageOffset":597800,"sourceLine":116,"sourceFile":"datastore.cc","symbol":"firebase::firestore::remote::Datastore::Start()::$_0::operator()() const","imageIndex":20,"symbolLocation":24},{"imageOffset":597765,"sourceLine":394,"sourceFile":"invoke.h","symbol":"decltype(std::declval<firebase::firestore::remote::Datastore::Start()::$_0&>()()) std::__1::__invoke[abi:v160006]<firebase::firestore::remote::Datastore::Start()::$_0&>(firebase::firestore::remote::Datastore::Start()::$_0&)","imageIndex":20,"symbolLocation":21},{"imageOffset":597701,"sourceLine":487,"sourceFile":"invoke.h","symbol":"void std::__1::__invoke_void_return_wrapper<void, true>::__call<firebase::firestore::remote::Datastore::Start()::$_0&>(firebase::firestore::remote::Datastore::Start()::$_0&)","imageIndex":20,"symbolLocation":21},{"imageOffset":597661,"sourceLine":185,"sourceFile":"function.h","symbol":"std::__1::__function::__alloc_func<firebase::firestore::remote::Datastore::Start()::$_0, std::__1::allocator<firebase::firestore::remote::Datastore::Start()::$_0>, void ()>::operator()[abi:v160006]()","imageIndex":20,"symbolLocation":29},{"imageOffset":593593,"sourceLine":356,"sourceFile":"function.h","symbol":"std::__1::__function::__func<firebase::firestore::remote::Datastore::Start()::$_0, std::__1::allocator<firebase::firestore::remote::Datastore::Start()::$_0>, void ()>::operator()()","imageIndex":20,"symbolLocation":25},{"imageOffset":76930,"sourceLine":510,"sourceFile":"function.h","symbol":"std::__1::__function::__value_func<void ()>::operator()[abi:v160006]() const","imageIndex":20,"symbolLocation":50},{"imageOffset":67365,"sourceLine":1156,"sourceFile":"function.h","symbol":"std::__1::function<void ()>::operator()() const","imageIndex":20,"symbolLocation":21},{"imageOffset":4485837,"sourceLine":102,"sourceFile":"task.cc","symbol":"firebase::firestore::util::Task::ExecuteAndRelease()","imageIndex":20,"symbolLocation":221},{"imageOffset":1013869,"sourceLine":237,"sourceFile":"executor_libdispatch.mm","symbol":"firebase::firestore::util::ExecutorLibdispatch::InvokeAsync(void*)","imageIndex":20,"symbolLocation":29},{"imageOffset":11578,"symbol":"_dispatch_client_callout","symbolLocation":8,"imageIndex":93},{"imageOffset":43100,"symbol":"_dispatch_lane_serial_drain","symbolLocation":1236,"imageIndex":93},{"imageOffset":46071,"symbol":"_dispatch_lane_invoke","symbolLocation":406,"imageIndex":93},{"imageOffset":94124,"symbol":"_dispatch_root_queue_drain_deferred_wlh","symbolLocation":276,"imageIndex":93},{"imageOffset":91506,"symbol":"_dispatch_workloop_worker_thread","symbolLocation":552,"imageIndex":93},{"imageOffset":11349,"symbol":"_pthread_wqthread","symbolLocation":327,"imageIndex":86},{"imageOffset":7103,"symbol":"start_wqthread","symbolLocation":15,"imageIndex":86}]},{"id":1716417,"frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":8124877,"sourceLine":114,"sourceFile":"sync.cc","symbol":"gpr_cv_wait","imageIndex":60,"symbolLocation":109},{"imageOffset":3637610,"sourceLine":230,"sourceFile":"executor.cc","symbol":"grpc_core::Executor::ThreadMain(void*)","imageIndex":60,"symbolLocation":458},{"imageOffset":8296881,"sourceLine":148,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const","imageIndex":60,"symbolLocation":225},{"imageOffset":8296649,"sourceLine":118,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1716418,"frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":8124877,"sourceLine":114,"sourceFile":"sync.cc","symbol":"gpr_cv_wait","imageIndex":60,"symbolLocation":109},{"imageOffset":3637610,"sourceLine":230,"sourceFile":"executor.cc","symbol":"grpc_core::Executor::ThreadMain(void*)","imageIndex":60,"symbolLocation":458},{"imageOffset":8296881,"sourceLine":148,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const","imageIndex":60,"symbolLocation":225},{"imageOffset":8296649,"sourceLine":118,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1716419,"frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":8124877,"sourceLine":114,"sourceFile":"sync.cc","symbol":"gpr_cv_wait","imageIndex":60,"symbolLocation":109},{"imageOffset":8396123,"sourceLine":204,"sourceFile":"timer_manager.cc","symbol":"wait_until(grpc_core::Timestamp)","imageIndex":60,"symbolLocation":491},{"imageOffset":8394991,"sourceLine":258,"sourceFile":"timer_manager.cc","symbol":"timer_main_loop()","imageIndex":60,"symbolLocation":191},{"imageOffset":8394735,"sourceLine":287,"sourceFile":"timer_manager.cc","symbol":"timer_thread(void*)","imageIndex":60,"symbolLocation":31},{"imageOffset":8296881,"sourceLine":148,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const","imageIndex":60,"symbolLocation":225},{"imageOffset":8296649,"sourceLine":118,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1716420,"frames":[{"imageOffset":7088,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":86}]},{"id":1716421,"frames":[{"imageOffset":7088,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":86}]},{"id":1716425,"frames":[{"imageOffset":7088,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":86}]},{"id":1716474,"queue":"com.apple.assistant.analytics.client","frames":[{"imageOffset":1672914,"symbol":"___AFMachAbsoluteTimeRate_block_invoke","symbolLocation":0,"imageIndex":105},{"imageOffset":11578,"symbol":"_dispatch_client_callout","symbolLocation":8,"imageIndex":93},{"imageOffset":16627,"symbol":"_dispatch_once_callout","symbolLocation":20,"imageIndex":93},{"imageOffset":1193713,"symbol":"_AFAnalyticsEventCreate","symbolLocation":641,"imageIndex":105},{"imageOffset":1194747,"symbol":"__71-[AFAnalytics logEventWithType:machAbsoluteTime:context:contextNoCopy:]_block_invoke","symbolLocation":27,"imageIndex":105},{"imageOffset":6800,"symbol":"_dispatch_call_block_and_release","symbolLocation":12,"imageIndex":93},{"imageOffset":11578,"symbol":"_dispatch_client_callout","symbolLocation":8,"imageIndex":93},{"imageOffset":43100,"symbol":"_dispatch_lane_serial_drain","symbolLocation":1236,"imageIndex":93},{"imageOffset":46071,"symbol":"_dispatch_lane_invoke","symbolLocation":406,"imageIndex":93},{"imageOffset":94124,"symbol":"_dispatch_root_queue_drain_deferred_wlh","symbolLocation":276,"imageIndex":93},{"imageOffset":91506,"symbol":"_dispatch_workloop_worker_thread","symbolLocation":552,"imageIndex":93},{"imageOffset":11349,"symbol":"_pthread_wqthread","symbolLocation":327,"imageIndex":86},{"imageOffset":7103,"symbol":"start_wqthread","symbolLocation":15,"imageIndex":86}]},{"id":1716519,"frames":[{"imageOffset":7088,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":86}]},{"id":1716539,"frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":8125083,"sourceLine":129,"sourceFile":"sync.cc","symbol":"gpr_cv_wait","imageIndex":60,"symbolLocation":315},{"imageOffset":3709226,"sourceLine":139,"sourceFile":"sync.h","symbol":"grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration)","imageIndex":60,"symbolLocation":122},{"imageOffset":8826834,"sourceLine":627,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkSignal::WaitWithTimeout(grpc_core::Duration)","imageIndex":60,"symbolLocation":130},{"imageOffset":8825522,"sourceLine":562,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step()","imageIndex":60,"symbolLocation":482},{"imageOffset":8824249,"sourceLine":495,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody()","imageIndex":60,"symbolLocation":201},{"imageOffset":8833233,"sourceLine":260,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const","imageIndex":60,"symbolLocation":33},{"imageOffset":8833193,"sourceLine":258,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":8296881,"sourceLine":148,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const","imageIndex":60,"symbolLocation":225},{"imageOffset":8296649,"sourceLine":118,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1716540,"frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":8125083,"sourceLine":129,"sourceFile":"sync.cc","symbol":"gpr_cv_wait","imageIndex":60,"symbolLocation":315},{"imageOffset":3709226,"sourceLine":139,"sourceFile":"sync.h","symbol":"grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration)","imageIndex":60,"symbolLocation":122},{"imageOffset":8826834,"sourceLine":627,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkSignal::WaitWithTimeout(grpc_core::Duration)","imageIndex":60,"symbolLocation":130},{"imageOffset":8825522,"sourceLine":562,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step()","imageIndex":60,"symbolLocation":482},{"imageOffset":8824249,"sourceLine":495,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody()","imageIndex":60,"symbolLocation":201},{"imageOffset":8833233,"sourceLine":260,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const","imageIndex":60,"symbolLocation":33},{"imageOffset":8833193,"sourceLine":258,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":8296881,"sourceLine":148,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const","imageIndex":60,"symbolLocation":225},{"imageOffset":8296649,"sourceLine":118,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1716541,"frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":8125083,"sourceLine":129,"sourceFile":"sync.cc","symbol":"gpr_cv_wait","imageIndex":60,"symbolLocation":315},{"imageOffset":3709226,"sourceLine":139,"sourceFile":"sync.h","symbol":"grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration)","imageIndex":60,"symbolLocation":122},{"imageOffset":8826834,"sourceLine":627,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkSignal::WaitWithTimeout(grpc_core::Duration)","imageIndex":60,"symbolLocation":130},{"imageOffset":8825522,"sourceLine":562,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step()","imageIndex":60,"symbolLocation":482},{"imageOffset":8824249,"sourceLine":495,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody()","imageIndex":60,"symbolLocation":201},{"imageOffset":8833233,"sourceLine":260,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const","imageIndex":60,"symbolLocation":33},{"imageOffset":8833193,"sourceLine":258,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":8296881,"sourceLine":148,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const","imageIndex":60,"symbolLocation":225},{"imageOffset":8296649,"sourceLine":118,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1716542,"frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":8125083,"sourceLine":129,"sourceFile":"sync.cc","symbol":"gpr_cv_wait","imageIndex":60,"symbolLocation":315},{"imageOffset":3709226,"sourceLine":139,"sourceFile":"sync.h","symbol":"grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration)","imageIndex":60,"symbolLocation":122},{"imageOffset":8384692,"sourceLine":60,"sourceFile":"timer_manager.cc","symbol":"grpc_event_engine::experimental::TimerManager::WaitUntil(grpc_core::Timestamp)","imageIndex":60,"symbolLocation":292},{"imageOffset":8391470,"sourceLine":79,"sourceFile":"timer_manager.cc","symbol":"grpc_event_engine::experimental::TimerManager::MainLoop()::$_0::operator()() const","imageIndex":60,"symbolLocation":62},{"imageOffset":8391397,"sourceLine":185,"sourceFile":"invoke.h","symbol":"decltype(std::declval<grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&>()()) absl::lts_20240116::base_internal::Callable::Invoke<grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&>(grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&)","imageIndex":60,"symbolLocation":21},{"imageOffset":8391365,"sourceLine":212,"sourceFile":"invoke.h","symbol":"decltype(Invoker<grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&>::type::Invoke(std::declval<grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&>())) absl::lts_20240116::base_internal::invoke<grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&>(grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&)","imageIndex":60,"symbolLocation":21},{"imageOffset":8391333,"sourceLine":132,"sourceFile":"any_invocable.h","symbol":"void absl::lts_20240116::internal_any_invocable::InvokeR<void, grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&, void>(grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&)","imageIndex":60,"symbolLocation":21},{"imageOffset":8390336,"sourceLine":368,"sourceFile":"any_invocable.h","symbol":"void absl::lts_20240116::internal_any_invocable::RemoteInvoker<false, void, grpc_event_engine::experimental::TimerManager::MainLoop()::$_0&>(absl::lts_20240116::internal_any_invocable::TypeErasedState*)","imageIndex":60,"symbolLocation":32},{"imageOffset":470974,"sourceLine":868,"sourceFile":"any_invocable.h","symbol":"absl::lts_20240116::internal_any_invocable::Impl<void ()>::operator()()","imageIndex":60,"symbolLocation":94},{"imageOffset":470621,"sourceLine":56,"sourceFile":"common_closures.h","symbol":"grpc_event_engine::experimental::SelfDeletingClosure::Run()","imageIndex":60,"symbolLocation":29},{"imageOffset":8825919,"sourceLine":581,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step()","imageIndex":60,"symbolLocation":879},{"imageOffset":8824249,"sourceLine":495,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody()","imageIndex":60,"symbolLocation":201},{"imageOffset":8833233,"sourceLine":260,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const","imageIndex":60,"symbolLocation":33},{"imageOffset":8833193,"sourceLine":258,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":8296881,"sourceLine":148,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const","imageIndex":60,"symbolLocation":225},{"imageOffset":8296649,"sourceLine":118,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1716543,"frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":8125083,"sourceLine":129,"sourceFile":"sync.cc","symbol":"gpr_cv_wait","imageIndex":60,"symbolLocation":315},{"imageOffset":3709226,"sourceLine":139,"sourceFile":"sync.h","symbol":"grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration)","imageIndex":60,"symbolLocation":122},{"imageOffset":8826834,"sourceLine":627,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkSignal::WaitWithTimeout(grpc_core::Duration)","imageIndex":60,"symbolLocation":130},{"imageOffset":8825522,"sourceLine":562,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step()","imageIndex":60,"symbolLocation":482},{"imageOffset":8824249,"sourceLine":495,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody()","imageIndex":60,"symbolLocation":201},{"imageOffset":8833233,"sourceLine":260,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const","imageIndex":60,"symbolLocation":33},{"imageOffset":8833193,"sourceLine":258,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":8296881,"sourceLine":148,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const","imageIndex":60,"symbolLocation":225},{"imageOffset":8296649,"sourceLine":118,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1716544,"frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":8125083,"sourceLine":129,"sourceFile":"sync.cc","symbol":"gpr_cv_wait","imageIndex":60,"symbolLocation":315},{"imageOffset":3709226,"sourceLine":139,"sourceFile":"sync.h","symbol":"grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration)","imageIndex":60,"symbolLocation":122},{"imageOffset":8826834,"sourceLine":627,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkSignal::WaitWithTimeout(grpc_core::Duration)","imageIndex":60,"symbolLocation":130},{"imageOffset":8825522,"sourceLine":562,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step()","imageIndex":60,"symbolLocation":482},{"imageOffset":8824249,"sourceLine":495,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody()","imageIndex":60,"symbolLocation":201},{"imageOffset":8833233,"sourceLine":260,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const","imageIndex":60,"symbolLocation":33},{"imageOffset":8833193,"sourceLine":258,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":8296881,"sourceLine":148,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const","imageIndex":60,"symbolLocation":225},{"imageOffset":8296649,"sourceLine":118,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1716545,"frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":8125083,"sourceLine":129,"sourceFile":"sync.cc","symbol":"gpr_cv_wait","imageIndex":60,"symbolLocation":315},{"imageOffset":3709226,"sourceLine":139,"sourceFile":"sync.h","symbol":"grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration)","imageIndex":60,"symbolLocation":122},{"imageOffset":8826834,"sourceLine":627,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkSignal::WaitWithTimeout(grpc_core::Duration)","imageIndex":60,"symbolLocation":130},{"imageOffset":8825522,"sourceLine":562,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step()","imageIndex":60,"symbolLocation":482},{"imageOffset":8824249,"sourceLine":495,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody()","imageIndex":60,"symbolLocation":201},{"imageOffset":8833233,"sourceLine":260,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const","imageIndex":60,"symbolLocation":33},{"imageOffset":8833193,"sourceLine":258,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":8296881,"sourceLine":148,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const","imageIndex":60,"symbolLocation":225},{"imageOffset":8296649,"sourceLine":118,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1716546,"frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":8125083,"sourceLine":129,"sourceFile":"sync.cc","symbol":"gpr_cv_wait","imageIndex":60,"symbolLocation":315},{"imageOffset":3709226,"sourceLine":139,"sourceFile":"sync.h","symbol":"grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration)","imageIndex":60,"symbolLocation":122},{"imageOffset":8826834,"sourceLine":627,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkSignal::WaitWithTimeout(grpc_core::Duration)","imageIndex":60,"symbolLocation":130},{"imageOffset":8825522,"sourceLine":562,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::Step()","imageIndex":60,"symbolLocation":482},{"imageOffset":8824249,"sourceLine":495,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::ThreadState::ThreadBody()","imageIndex":60,"symbolLocation":201},{"imageOffset":8833233,"sourceLine":260,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::operator()(void*) const","imageIndex":60,"symbolLocation":33},{"imageOffset":8833193,"sourceLine":258,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::StartThread()::$_0::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":8296881,"sourceLine":148,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const","imageIndex":60,"symbolLocation":225},{"imageOffset":8296649,"sourceLine":118,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1716547,"frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":8125083,"sourceLine":129,"sourceFile":"sync.cc","symbol":"gpr_cv_wait","imageIndex":60,"symbolLocation":315},{"imageOffset":3709226,"sourceLine":139,"sourceFile":"sync.h","symbol":"grpc_core::CondVar::WaitWithTimeout(grpc_core::Mutex*, absl::lts_20240116::Duration)","imageIndex":60,"symbolLocation":122},{"imageOffset":8822597,"sourceLine":48,"sourceFile":"notification.h","symbol":"grpc_core::Notification::WaitForNotificationWithTimeout(absl::lts_20240116::Duration)","imageIndex":60,"symbolLocation":629},{"imageOffset":8821857,"sourceLine":403,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::Lifeguard::LifeguardMain()","imageIndex":60,"symbolLocation":241},{"imageOffset":8833425,"sourceLine":387,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::Lifeguard::Start()::$_1::operator()(void*) const","imageIndex":60,"symbolLocation":33},{"imageOffset":8833385,"sourceLine":385,"sourceFile":"work_stealing_thread_pool.cc","symbol":"grpc_event_engine::experimental::WorkStealingThreadPool::WorkStealingThreadPoolImpl::Lifeguard::Start()::$_1::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":8296881,"sourceLine":148,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const","imageIndex":60,"symbolLocation":225},{"imageOffset":8296649,"sourceLine":118,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1716772,"frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26456,"symbol":"_pthread_cond_wait","symbolLocation":1242,"imageIndex":86},{"imageOffset":8125083,"sourceLine":129,"sourceFile":"sync.cc","symbol":"gpr_cv_wait","imageIndex":60,"symbolLocation":315},{"imageOffset":8396123,"sourceLine":204,"sourceFile":"timer_manager.cc","symbol":"wait_until(grpc_core::Timestamp)","imageIndex":60,"symbolLocation":491},{"imageOffset":8394991,"sourceLine":258,"sourceFile":"timer_manager.cc","symbol":"timer_main_loop()","imageIndex":60,"symbolLocation":191},{"imageOffset":8394735,"sourceLine":287,"sourceFile":"timer_manager.cc","symbol":"timer_thread(void*)","imageIndex":60,"symbolLocation":31},{"imageOffset":8296881,"sourceLine":148,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::operator()(void*) const","imageIndex":60,"symbolLocation":225},{"imageOffset":8296649,"sourceLine":118,"sourceFile":"thd.cc","symbol":"grpc_core::(anonymous namespace)::ThreadInternalsPosix::ThreadInternalsPosix(char const*, void (*)(void*), void*, bool*, grpc_core::Thread::Options const&)::'lambda'(void*)::__invoke(void*)","imageIndex":60,"symbolLocation":25},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]},{"id":1716778,"name":"DartWorker","frames":[{"imageOffset":16526,"symbol":"__psynch_cvwait","symbolLocation":10,"imageIndex":85},{"imageOffset":26509,"symbol":"_pthread_cond_wait","symbolLocation":1295,"imageIndex":86},{"imageOffset":7073774,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":126,"imageIndex":83},{"imageOffset":8753640,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":600,"imageIndex":83},{"imageOffset":8753971,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":115,"imageIndex":83},{"imageOffset":8261774,"symbol":"dart::ThreadStart(void*)","symbolLocation":206,"imageIndex":83},{"imageOffset":25043,"symbol":"_pthread_start","symbolLocation":125,"imageIndex":86},{"imageOffset":7123,"symbol":"thread_start","symbolLocation":15,"imageIndex":86}]}],
  "usedImages" : [
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4748955648,
    "size" : 638976,
    "uuid" : "d4fdfb7a-666b-3be3-969d-5728d56e85fd",
    "path" : "\/usr\/lib\/dyld",
    "name" : "dyld"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4984406016,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.AutoFillUI",
    "size" : 204800,
    "uuid" : "c504f7ed-8ad4-3937-89a7-e578d91f8ffc",
    "path" : "\/Volumes\/VOLUME\/*\/AutoFillUI.framework\/AutoFillUI",
    "name" : "AutoFillUI",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 5113081856,
    "size" : 1388544,
    "uuid" : "d7aa814e-209f-325c-a7ff-247fac40a60d",
    "path" : "\/Volumes\/VOLUME\/*\/libquic.dylib",
    "name" : "libquic.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4502585344,
    "CFBundleShortVersionString" : "341.35",
    "CFBundleIdentifier" : "com.apple.MTLSimDriver",
    "size" : 225280,
    "uuid" : "b36bfdcc-4219-3c83-b4cf-ba66bc6a7336",
    "path" : "\/Volumes\/VOLUME\/*\/MTLSimDriver.framework\/MTLSimDriver",
    "name" : "MTLSimDriver",
    "CFBundleVersion" : "341.35"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4579041280,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.gpusw.MetalSerializer",
    "size" : 184320,
    "uuid" : "0b871c05-45c6-336a-9377-952af116b122",
    "path" : "\/Volumes\/VOLUME\/*\/MetalSerializer.framework\/MetalSerializer",
    "name" : "MetalSerializer",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4515573760,
    "size" : 53248,
    "uuid" : "d90c52be-fbd1-30b8-83a1-89789387ee29",
    "path" : "\/Volumes\/VOLUME\/*\/libobjc-trampolines.dylib",
    "name" : "libobjc-trampolines.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4497924096,
    "CFBundleShortVersionString" : "10.19.2",
    "CFBundleIdentifier" : "org.cocoapods.AppCheckCore",
    "size" : 131072,
    "uuid" : "75e10be4-01bc-38d6-be72-bf1497254837",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/AppCheckCore.framework\/AppCheckCore",
    "name" : "AppCheckCore",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4504784896,
    "CFBundleShortVersionString" : "4.3.9",
    "CFBundleIdentifier" : "org.cocoapods.DKImagePickerController",
    "size" : 638976,
    "uuid" : "e7aa23fb-40c7-3ddb-82ff-1c27f22ca504",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/DKImagePickerController.framework\/DKImagePickerController",
    "name" : "DKImagePickerController",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4506357760,
    "CFBundleShortVersionString" : "0.0.19",
    "CFBundleIdentifier" : "org.cocoapods.DKPhotoGallery",
    "size" : 557056,
    "uuid" : "1ee82643-2f3f-3711-870a-3fcd63bd6746",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/DKPhotoGallery.framework\/DKPhotoGallery",
    "name" : "DKPhotoGallery",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4503212032,
    "CFBundleShortVersionString" : "2.4.0",
    "CFBundleIdentifier" : "org.cocoapods.FBLPromises",
    "size" : 81920,
    "uuid" : "0a31bac6-b6cf-3a64-919b-b28e32a27beb",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FBLPromises.framework\/FBLPromises",
    "name" : "FBLPromises",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4497727488,
    "CFBundleShortVersionString" : "10.29.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseABTesting",
    "size" : 49152,
    "uuid" : "3bb1b755-5544-3766-ae0d-817d43f83208",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseABTesting.framework\/FirebaseABTesting",
    "name" : "FirebaseABTesting",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4503425024,
    "CFBundleShortVersionString" : "10.25.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseAppCheck",
    "size" : 49152,
    "uuid" : "b9861eb1-eb6f-3df8-84fb-dc17a55aad45",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseAppCheck.framework\/FirebaseAppCheck",
    "name" : "FirebaseAppCheck",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4498251776,
    "CFBundleShortVersionString" : "10.29.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseAppCheckInterop",
    "size" : 16384,
    "uuid" : "1f8cbc29-619d-3ea1-9fea-fee8a564c3be",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseAppCheckInterop.framework\/FirebaseAppCheckInterop",
    "name" : "FirebaseAppCheckInterop",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4507668480,
    "CFBundleShortVersionString" : "10.25.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseAuth",
    "size" : 524288,
    "uuid" : "4c9da416-d3da-39a1-abfe-f8849e8dac67",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseAuth.framework\/FirebaseAuth",
    "name" : "FirebaseAuth",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4498333696,
    "CFBundleShortVersionString" : "10.29.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseAuthInterop",
    "size" : 16384,
    "uuid" : "610ec85a-ddbb-36fd-8500-984a154c7fc6",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseAuthInterop.framework\/FirebaseAuthInterop",
    "name" : "FirebaseAuthInterop",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4503777280,
    "CFBundleShortVersionString" : "10.25.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseCore",
    "size" : 81920,
    "uuid" : "09ac6880-65a6-3fc7-a583-45015de80fff",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseCore.framework\/FirebaseCore",
    "name" : "FirebaseCore",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4503572480,
    "CFBundleShortVersionString" : "10.29.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseCoreExtension",
    "size" : 16384,
    "uuid" : "32ac2ee5-d42b-3ed9-a6ad-a64f8d0e0f75",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseCoreExtension.framework\/FirebaseCoreExtension",
    "name" : "FirebaseCoreExtension",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4508798976,
    "CFBundleShortVersionString" : "10.29.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseCoreInternal",
    "size" : 147456,
    "uuid" : "add9505c-e64a-3e05-aff6-fd66b04dd928",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseCoreInternal.framework\/FirebaseCoreInternal",
    "name" : "FirebaseCoreInternal",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4510240768,
    "CFBundleShortVersionString" : "10.25.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseCrashlytics",
    "size" : 458752,
    "uuid" : "40080184-cc79-37bd-a60a-41d9c6acf2d8",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseCrashlytics.framework\/FirebaseCrashlytics",
    "name" : "FirebaseCrashlytics",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4509257728,
    "CFBundleShortVersionString" : "10.25.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseFirestore",
    "size" : 147456,
    "uuid" : "1c9f1206-f0e9-3420-8475-002b61dce148",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseFirestore.framework\/FirebaseFirestore",
    "name" : "FirebaseFirestore",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4534599680,
    "CFBundleShortVersionString" : "10.25.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseFirestoreInternal",
    "size" : 5586944,
    "uuid" : "9b643534-2a43-34ef-b743-d5c4a42f0895",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseFirestoreInternal.framework\/FirebaseFirestoreInternal",
    "name" : "FirebaseFirestoreInternal",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4504248320,
    "CFBundleShortVersionString" : "10.29.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseInstallations",
    "size" : 114688,
    "uuid" : "07bca220-514d-309c-9dae-4d373fc0bc1a",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseInstallations.framework\/FirebaseInstallations",
    "name" : "FirebaseInstallations",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4511207424,
    "CFBundleShortVersionString" : "10.25.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseMessaging",
    "size" : 262144,
    "uuid" : "58d6d07e-8dc8-3a80-8f59-7b239a274965",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseMessaging.framework\/FirebaseMessaging",
    "name" : "FirebaseMessaging",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4511731712,
    "CFBundleShortVersionString" : "10.25.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebasePerformance",
    "size" : 245760,
    "uuid" : "3b9fc2bc-35a8-3ac6-8c34-1702102a0d05",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebasePerformance.framework\/FirebasePerformance",
    "name" : "FirebasePerformance",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4512256000,
    "CFBundleShortVersionString" : "10.29.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseRemoteConfig",
    "size" : 245760,
    "uuid" : "5736b99f-956a-3439-bab4-7b891c179043",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseRemoteConfig.framework\/FirebaseRemoteConfig",
    "name" : "FirebaseRemoteConfig",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4503973888,
    "CFBundleShortVersionString" : "10.29.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseRemoteConfigInterop",
    "size" : 32768,
    "uuid" : "9e59578b-212d-386e-abfe-d90d76f1d585",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseRemoteConfigInterop.framework\/FirebaseRemoteConfigInterop",
    "name" : "FirebaseRemoteConfigInterop",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4513325056,
    "CFBundleShortVersionString" : "10.29.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseSessions",
    "size" : 196608,
    "uuid" : "c630b278-d778-3ff2-b19e-2729bb487a22",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseSessions.framework\/FirebaseSessions",
    "name" : "FirebaseSessions",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4514639872,
    "CFBundleShortVersionString" : "10.29.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseSharedSwift",
    "size" : 344064,
    "uuid" : "8277e1c0-f9e2-3258-8216-08ad8bcc81b7",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseSharedSwift.framework\/FirebaseSharedSwift",
    "name" : "FirebaseSharedSwift",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4516323328,
    "CFBundleShortVersionString" : "10.25.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseStorage",
    "size" : 409600,
    "uuid" : "f7728d28-fa96-3e26-bd8f-6f075722c755",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/FirebaseStorage.framework\/FirebaseStorage",
    "name" : "FirebaseStorage",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4512763904,
    "CFBundleShortVersionString" : "2.3.0",
    "CFBundleIdentifier" : "org.cocoapods.GTMSessionFetcher",
    "size" : 294912,
    "uuid" : "243ba06f-9dd6-3900-aa08-710d42942881",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/GTMSessionFetcher.framework\/GTMSessionFetcher",
    "name" : "GTMSessionFetcher",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4513898496,
    "CFBundleShortVersionString" : "9.4.1",
    "CFBundleIdentifier" : "org.cocoapods.GoogleDataTransport",
    "size" : 196608,
    "uuid" : "d64665a1-3bbf-3dbc-bb9a-96185e7bb564",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/GoogleDataTransport.framework\/GoogleDataTransport",
    "name" : "GoogleDataTransport",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4509700096,
    "CFBundleShortVersionString" : "2.3.2",
    "CFBundleIdentifier" : "org.cocoapods.GoogleToolboxForMac",
    "size" : 49152,
    "uuid" : "375b7437-a632-38fe-b125-9fed666fe52f",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/GoogleToolboxForMac.framework\/GoogleToolboxForMac",
    "name" : "GoogleToolboxForMac",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4514308096,
    "CFBundleShortVersionString" : "7.13.3",
    "CFBundleIdentifier" : "org.cocoapods.GoogleUtilities",
    "size" : 147456,
    "uuid" : "41bfdef8-1fa1-3c68-a8e1-06146c386f36",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/GoogleUtilities.framework\/GoogleUtilities",
    "name" : "GoogleUtilities",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4509847552,
    "CFBundleShortVersionString" : "2.4.0",
    "CFBundleIdentifier" : "org.cocoapods.Promises",
    "size" : 98304,
    "uuid" : "55e1260a-8ea0-34a5-ac91-e1ac0d1c081f",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/Promises.framework\/Promises",
    "name" : "Promises",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4515377152,
    "CFBundleShortVersionString" : "5.2.4",
    "CFBundleIdentifier" : "org.cocoapods.Reachability",
    "size" : 49152,
    "uuid" : "cd1c0764-6a2d-39eb-bc47-6c336ffc9e22",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/Reachability.framework\/Reachability",
    "name" : "Reachability",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4503654400,
    "CFBundleShortVersionString" : "100.0.0",
    "CFBundleIdentifier" : "org.cocoapods.RecaptchaInterop",
    "size" : 16384,
    "uuid" : "e8dded0b-228e-3e2c-9c1a-dc70ee8cc3ce",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/RecaptchaInterop.framework\/RecaptchaInterop",
    "name" : "RecaptchaInterop",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4518260736,
    "CFBundleShortVersionString" : "5.21.1",
    "CFBundleIdentifier" : "org.cocoapods.SDWebImage",
    "size" : 524288,
    "uuid" : "3a9ac2db-52cb-389c-bf12-61173a34cb03",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/SDWebImage.framework\/SDWebImage",
    "name" : "SDWebImage",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4517257216,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.Stripe",
    "size" : 180224,
    "uuid" : "cbea4635-002f-3836-87fa-b4beaa0518d5",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/Stripe.framework\/Stripe",
    "name" : "Stripe",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4520853504,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.StripeApplePay",
    "size" : 720896,
    "uuid" : "3434dc76-92a0-3133-b4e1-2cf5fa1e2bd3",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/StripeApplePay.framework\/StripeApplePay",
    "name" : "StripeApplePay",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4524724224,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.StripeCore",
    "size" : 950272,
    "uuid" : "1b531740-9ec6-38ac-863e-8d6ee6969861",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/StripeCore.framework\/StripeCore",
    "name" : "StripeCore",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4567277568,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.StripeFinancialConnections",
    "size" : 4325376,
    "uuid" : "86fb9ad6-87cb-3d47-bec3-0526c4232ceb",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/StripeFinancialConnections.framework\/StripeFinancialConnections",
    "name" : "StripeFinancialConnections",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4588642304,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.StripePaymentSheet",
    "size" : 5390336,
    "uuid" : "b425b88c-1492-3625-ba49-de63ee4e7bf3",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/StripePaymentSheet.framework\/StripePaymentSheet",
    "name" : "StripePaymentSheet",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4557979648,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.StripePayments",
    "size" : 3063808,
    "uuid" : "11ea70d4-afbe-3ba0-8f00-fa7ddc97e6ed",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/StripePayments.framework\/StripePayments",
    "name" : "StripePayments",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4527001600,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.StripePaymentsUI",
    "size" : 917504,
    "uuid" : "6ed0ee93-7098-335b-918f-f696f5768464",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/StripePaymentsUI.framework\/StripePaymentsUI",
    "name" : "StripePaymentsUI",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4531601408,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.StripeUICore",
    "size" : 966656,
    "uuid" : "25f1ae51-9dc6-3792-a43d-c570feeedef7",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/StripeUICore.framework\/StripeUICore",
    "name" : "StripeUICore",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4515856384,
    "CFBundleShortVersionString" : "5.4.5",
    "CFBundleIdentifier" : "org.cocoapods.SwiftyGif",
    "size" : 98304,
    "uuid" : "a7ce88d6-ef15-3450-936e-7a31cf1cea07",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/SwiftyGif.framework\/SwiftyGif",
    "name" : "SwiftyGif",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4504104960,
    "CFBundleShortVersionString" : "2.1.1",
    "CFBundleIdentifier" : "org.cocoapods.Try",
    "size" : 16384,
    "uuid" : "30cf515b-6090-3139-a4b0-76802e4eba2e",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/Try.framework\/Try",
    "name" : "Try",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4580122624,
    "CFBundleShortVersionString" : "1.20240116.2",
    "CFBundleIdentifier" : "org.cocoapods.absl",
    "size" : 1196032,
    "uuid" : "5d2eddb1-ed92-3e20-90e9-1ee51a74ecdc",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/absl.framework\/absl",
    "name" : "absl",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4504510464,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.audio-session",
    "size" : 49152,
    "uuid" : "60514623-3e98-309b-8e55-61e5bab4812c",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/audio_session.framework\/audio_session",
    "name" : "audio_session",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4504641536,
    "CFBundleShortVersionString" : "1.0.0",
    "CFBundleIdentifier" : "org.cocoapods.battery-plus",
    "size" : 16384,
    "uuid" : "63df91de-cc14-303e-8866-14d471eac9b6",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/battery_plus.framework\/battery_plus",
    "name" : "battery_plus",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4519903232,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.camera-avfoundation",
    "size" : 278528,
    "uuid" : "6b384d41-26f2-3a95-8998-3421cb3743d8",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/camera_avfoundation.framework\/camera_avfoundation",
    "name" : "camera_avfoundation",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4516134912,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.connectivity-plus",
    "size" : 49152,
    "uuid" : "d1f90adc-4293-3476-9cc9-01191c7de5cb",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/connectivity_plus.framework\/connectivity_plus",
    "name" : "connectivity_plus",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4510093312,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.device-info-plus",
    "size" : 16384,
    "uuid" : "4d06832e-8776-328d-8563-20f4cb24993f",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/device_info_plus.framework\/device_info_plus",
    "name" : "device_info_plus",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4497584128,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.disk-space",
    "size" : 32768,
    "uuid" : "9911bd3e-8855-34d6-a73f-234552395b4a",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/disk_space.framework\/disk_space",
    "name" : "disk_space",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4517699584,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.file-picker",
    "size" : 65536,
    "uuid" : "60f1dc58-f5ea-3f3d-8f9b-ba35a79b4be7",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/file_picker.framework\/file_picker",
    "name" : "file_picker",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4517863424,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.flutter-local-notifications",
    "size" : 65536,
    "uuid" : "940d24ae-a309-316f-bd5d-d7d62a044166",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/flutter_local_notifications.framework\/flutter_local_notifications",
    "name" : "flutter_local_notifications",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4515667968,
    "CFBundleShortVersionString" : "2.4.3",
    "CFBundleIdentifier" : "org.cocoapods.flutter-native-splash",
    "size" : 16384,
    "uuid" : "681206d7-174d-3096-b0bd-b8f2aa74d53b",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/flutter_native_splash.framework\/flutter_native_splash",
    "name" : "flutter_native_splash",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4519260160,
    "CFBundleShortVersionString" : "1.0.2",
    "CFBundleIdentifier" : "org.cocoapods.flutter-pdfview",
    "size" : 32768,
    "uuid" : "80585b9d-8b6b-3808-9d10-a7d9fd7a11c4",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/flutter_pdfview.framework\/flutter_pdfview",
    "name" : "flutter_pdfview",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4519374848,
    "CFBundleShortVersionString" : "6.0.0",
    "CFBundleIdentifier" : "org.cocoapods.flutter-secure-storage",
    "size" : 81920,
    "uuid" : "cc562d9a-c454-3ba2-ab47-9af124408377",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/flutter_secure_storage.framework\/flutter_secure_storage",
    "name" : "flutter_secure_storage",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4519587840,
    "CFBundleShortVersionString" : "1.2.0",
    "CFBundleIdentifier" : "org.cocoapods.geolocator-apple",
    "size" : 49152,
    "uuid" : "a29056e5-51a8-303c-905e-f7f2ea244e78",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/geolocator_apple.framework\/geolocator_apple",
    "name" : "geolocator_apple",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4649713664,
    "CFBundleShortVersionString" : "1.62.5",
    "CFBundleIdentifier" : "org.cocoapods.grpc",
    "size" : 12288000,
    "uuid" : "da065099-9e25-333a-8292-035a97174215",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/grpc.framework\/grpc",
    "name" : "grpc",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4600668160,
    "CFBundleShortVersionString" : "1.62.5",
    "CFBundleIdentifier" : "org.cocoapods.grpcpp",
    "size" : 901120,
    "uuid" : "2c9653a3-535f-3b60-a344-9ff36b49ed14",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/grpcpp.framework\/grpcpp",
    "name" : "grpcpp",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4520542208,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.image-picker-ios",
    "size" : 81920,
    "uuid" : "8c646ddb-6c2b-3f83-b97f-32fefc2e9eb6",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/image_picker_ios.framework\/image_picker_ios",
    "name" : "image_picker_ios",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4518027264,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.integration-test",
    "size" : 16384,
    "uuid" : "1b75a54b-fd85-377a-bbee-8b11520e9b5f",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/integration_test.framework\/integration_test",
    "name" : "integration_test",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4522692608,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.just-audio",
    "size" : 98304,
    "uuid" : "cd53970f-9393-39d7-9814-3c54ce0890da",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/just_audio.framework\/just_audio",
    "name" : "just_audio",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4529229824,
    "CFBundleShortVersionString" : "1.22.6",
    "CFBundleIdentifier" : "org.cocoapods.leveldb",
    "size" : 458752,
    "uuid" : "55f342b2-5367-3ad2-8b6f-d63e5ded1fb3",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/leveldb.framework\/leveldb",
    "name" : "leveldb",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4522475520,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.local-auth-darwin",
    "size" : 49152,
    "uuid" : "859ca225-782f-3652-b81a-b14181e4d882",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/local_auth_darwin.framework\/local_auth_darwin",
    "name" : "local_auth_darwin",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4515749888,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.location",
    "size" : 32768,
    "uuid" : "b7cf12a8-5203-3ef0-8c7c-0a1d9b5fa0c9",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/location.framework\/location",
    "name" : "location",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4519735296,
    "CFBundleShortVersionString" : "2.30910.0",
    "CFBundleIdentifier" : "org.cocoapods.nanopb",
    "size" : 32768,
    "uuid" : "c19d7393-a7c2-3121-a9f6-5efadf8d7017",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/nanopb.framework\/nanopb",
    "name" : "nanopb",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4583530496,
    "CFBundleShortVersionString" : "0.0.32",
    "CFBundleIdentifier" : "org.cocoapods.openssl-grpc",
    "size" : 1867776,
    "uuid" : "5a6ebaee-476a-3d97-9ce5-051fa1da3bbf",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/openssl_grpc.framework\/openssl_grpc",
    "name" : "openssl_grpc",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4518125568,
    "CFBundleShortVersionString" : "0.4.5",
    "CFBundleIdentifier" : "org.cocoapods.package-info-plus",
    "size" : 16384,
    "uuid" : "299382c2-d806-31c9-a849-be6ee7c859a7",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/package_info_plus.framework\/package_info_plus",
    "name" : "package_info_plus",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : **********,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.path-provider-foundation",
    "size" : 49152,
    "uuid" : "1d21bc22-143c-3502-aac8-345c3207695b",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/path_provider_foundation.framework\/path_provider_foundation",
    "name" : "path_provider_foundation",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : **********,
    "CFBundleShortVersionString" : "1.0.0",
    "CFBundleIdentifier" : "org.cocoapods.record-darwin",
    "size" : 163840,
    "uuid" : "a9086703-3f8f-32b1-acdf-71bb495a15b4",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/record_darwin.framework\/record_darwin",
    "name" : "record_darwin",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : **********,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.sensors-plus",
    "size" : 65536,
    "uuid" : "e1ea8ae8-e5a9-3e1e-9a92-909523c63f6f",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/sensors_plus.framework\/sensors_plus",
    "name" : "sensors_plus",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4520738816,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.share-plus",
    "size" : 32768,
    "uuid" : "e9337f50-0c6b-39d8-abf7-1f6308feafda",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/share_plus.framework\/share_plus",
    "name" : "share_plus",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4524285952,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.shared-preferences-foundation",
    "size" : 81920,
    "uuid" : "96e49fee-c3b2-315a-af3f-96038954a571",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/shared_preferences_foundation.framework\/shared_preferences_foundation",
    "name" : "shared_preferences_foundation",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4523245568,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.smart-auth",
    "size" : 32768,
    "uuid" : "571813f0-2466-3487-88fd-45dfbbdf8464",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/smart_auth.framework\/smart_auth",
    "name" : "smart_auth",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4530962432,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.speech-to-text",
    "size" : 147456,
    "uuid" : "c338d03d-25d4-3366-bf33-cf5afcf18277",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/speech_to_text.framework\/speech_to_text",
    "name" : "speech_to_text",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4533993472,
    "CFBundleShortVersionString" : "0.0.4",
    "CFBundleIdentifier" : "org.cocoapods.sqflite-darwin",
    "size" : 131072,
    "uuid" : "dd15ec20-c358-394d-87d8-80fe17f699e1",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/sqflite_darwin.framework\/sqflite_darwin",
    "name" : "sqflite_darwin",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4605009920,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.stripe-ios",
    "size" : 1343488,
    "uuid" : "c1c2ab53-d208-30e8-a75c-b141de6f7521",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/stripe_ios.framework\/stripe_ios",
    "name" : "stripe_ios",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4534272000,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.url-launcher-ios",
    "size" : 81920,
    "uuid" : "aac15d2d-77fe-388f-98e3-d10555d3319a",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/url_launcher_ios.framework\/url_launcher_ios",
    "name" : "url_launcher_ios",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4576698368,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.webview-flutter-wkwebview",
    "size" : 802816,
    "uuid" : "695a9978-2a59-3f96-91ec-ea97a797d1f1",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/webview_flutter_wkwebview.framework\/webview_flutter_wkwebview",
    "name" : "webview_flutter_wkwebview",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4565123072,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.workmanager",
    "size" : 114688,
    "uuid" : "f21a1b5e-ca6d-3720-b305-d59ea39c8e00",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/workmanager.framework\/workmanager",
    "name" : "workmanager",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4801474560,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "io.flutter.flutter",
    "size" : 36478976,
    "uuid" : "4c4c441b-5555-3144-a1b0-6645cf325e85",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Frameworks\/Flutter.framework\/Flutter",
    "name" : "Flutter",
    "CFBundleVersion" : "1.0"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4522905600,
    "size" : 49152,
    "uuid" : "9ae2c3dc-b88b-3c82-a0bd-918275fe7661",
    "path" : "\/usr\/lib\/system\/libsystem_platform.dylib",
    "name" : "libsystem_platform.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4578598912,
    "size" : 245760,
    "uuid" : "4b596cc5-f540-3f67-832e-1ec4159d2606",
    "path" : "\/usr\/lib\/system\/libsystem_kernel.dylib",
    "name" : "libsystem_kernel.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4524048384,
    "size" : 49152,
    "uuid" : "51a43b06-feb4-3836-9e4f-21b69bb13726",
    "path" : "\/usr\/lib\/system\/libsystem_pthread.dylib",
    "name" : "libsystem_pthread.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4524515328,
    "CFBundleShortVersionString" : "95",
    "CFBundleIdentifier" : "com.apple.mlcompiler.services",
    "size" : 106496,
    "uuid" : "7dcc25a8-4e35-3cca-a27d-8b9561e9d427",
    "path" : "\/Volumes\/VOLUME\/*\/MLCompilerServices.framework\/MLCompilerServices",
    "name" : "MLCompilerServices",
    "CFBundleVersion" : "95"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4498436096,
    "size" : 360448,
    "uuid" : "39c2d7d4-1de8-3a61-a08f-81f004308f58",
    "path" : "\/Volumes\/VOLUME\/*\/dyld_sim",
    "name" : "dyld_sim"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4415864832,
    "CFBundleShortVersionString" : "1.0.0",
    "CFBundleIdentifier" : "com.example.cultureConnect",
    "size" : 48562176,
    "uuid" : "c9708a29-e677-3d6e-944f-c43f98274bdb",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/9140C1FA-E7C6-41E3-8897-74037AD695D1\/data\/Containers\/Bundle\/Application\/E327BAC5-D75E-493A-88D4-1A5475A8B9CD\/Runner.app\/Runner",
    "name" : "Runner",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703129468928,
    "size" : 544768,
    "uuid" : "362f3aff-da5e-3175-81d6-8fbba4925a8a",
    "path" : "\/Volumes\/VOLUME\/*\/libsystem_c.dylib",
    "name" : "libsystem_c.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703131267072,
    "size" : 86012,
    "uuid" : "f326cb12-f529-3daf-8ea4-b1d7a5b0ae7e",
    "path" : "\/Volumes\/VOLUME\/*\/libc++abi.dylib",
    "name" : "libc++abi.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703128862720,
    "size" : 241626,
    "uuid" : "316eb0a5-f8ca-30f3-9c25-fc04f0aeff9e",
    "path" : "\/Volumes\/VOLUME\/*\/libobjc.A.dylib",
    "name" : "libobjc.A.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703130013696,
    "size" : 307198,
    "uuid" : "20de26b2-4cb5-3346-98fd-e4488b4ea5bd",
    "path" : "\/Volumes\/VOLUME\/*\/libdispatch.dylib",
    "name" : "libdispatch.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703132196864,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.CoreFoundation",
    "size" : 3731445,
    "uuid" : "75d267fd-12bc-3192-b349-3e80ee673a5a",
    "path" : "\/Volumes\/VOLUME\/*\/CoreFoundation.framework\/CoreFoundation",
    "name" : "CoreFoundation",
    "CFBundleVersion" : "2202"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703395586048,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.GraphicsServices",
    "size" : 32767,
    "uuid" : "70698d67-e95b-3d0d-b12c-a6ad85767806",
    "path" : "\/Volumes\/VOLUME\/*\/GraphicsServices.framework\/GraphicsServices",
    "name" : "GraphicsServices",
    "CFBundleVersion" : "1.0"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703208038400,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.UIKitCore",
    "size" : 31338494,
    "uuid" : "12622ad2-3713-30ef-b0eb-7cd0f7397650",
    "path" : "\/Volumes\/VOLUME\/*\/UIKitCore.framework\/UIKitCore",
    "name" : "UIKitCore",
    "CFBundleVersion" : "7209.1.104"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703256055808,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.Network",
    "size" : 11825137,
    "uuid" : "5a88ef53-c626-34e9-95b6-e773e4c5fd30",
    "path" : "\/Volumes\/VOLUME\/*\/Network.framework\/Network",
    "name" : "Network",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703478255616,
    "size" : 4952061,
    "uuid" : "5006b63c-6088-321c-9363-ce13d1cc9fc5",
    "path" : "\/Volumes\/VOLUME\/*\/libswiftCore.dylib",
    "name" : "libswiftCore.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703136428032,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.Foundation",
    "size" : 11943927,
    "uuid" : "30929a90-3f75-330c-ac6b-6a79bc83b134",
    "path" : "\/Volumes\/VOLUME\/*\/Foundation.framework\/Foundation",
    "name" : "Foundation",
    "CFBundleVersion" : "2202"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703129452544,
    "size" : 16377,
    "uuid" : "3ca9cb28-23b5-31fb-8214-3dd86b5f9a92",
    "path" : "\/Volumes\/VOLUME\/*\/libsystem_blocks.dylib",
    "name" : "libsystem_blocks.dylib"
  },
  {
    "size" : 0,
    "source" : "A",
    "base" : 0,
    "uuid" : "00000000-0000-0000-0000-000000000000"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703202742272,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.CFNetwork",
    "size" : 3833855,
    "uuid" : "b5ac37ec-798b-3f44-a3fa-71f9f6a47d3c",
    "path" : "\/Volumes\/VOLUME\/*\/CFNetwork.framework\/CFNetwork",
    "name" : "CFNetwork",
    "CFBundleVersion" : "1490.0.4"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703452475392,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.TextInput",
    "size" : 794614,
    "uuid" : "1b7d33f5-88b6-367c-9797-341e8010417d",
    "path" : "\/Volumes\/VOLUME\/*\/TextInput.framework\/TextInput",
    "name" : "TextInput",
    "CFBundleVersion" : "1.0"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703130320896,
    "size" : 233464,
    "uuid" : "edf78275-cd92-3239-a406-38717fa2742d",
    "path" : "\/Volumes\/VOLUME\/*\/libsystem_malloc.dylib",
    "name" : "libsystem_malloc.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703483207680,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.AssistantServices",
    "size" : 2465791,
    "uuid" : "6b89eee7-a88c-35ff-9841-1290dfc8a2bc",
    "path" : "\/Volumes\/VOLUME\/*\/AssistantServices.framework\/AssistantServices",
    "name" : "AssistantServices",
    "CFBundleVersion" : "1"
  }
],
  "sharedCache" : {
  "base" : 140703128616960,
  "size" : 3293069312,
  "uuid" : "97430d67-f1f8-3e25-838f-195709d0da43"
},
  "vmSummary" : "ReadOnly portion of Libraries: Total=1.7G resident=0K(0%) swapped_out_or_unallocated=1.7G(100%)\nWritable regions: Total=902.2M written=0K(0%) resident=0K(0%) swapped_out=0K(0%) unallocated=902.2M(100%)\n\n                                VIRTUAL   REGION \nREGION TYPE                        SIZE    COUNT (non-coalesced) \n===========                     =======  ======= \nAccelerate framework               128K        1 \nActivity Tracing                   256K        1 \nCG raster data                     184K        5 \nColorSync                           88K        6 \nCoreAnimation                       44K        1 \nFoundation                          16K        1 \nIOSurface                         41.5M        4 \nKernel Alloc Once                    8K        1 \nMALLOC                           225.3M       97 \nMALLOC guard page                   48K       11 \nMALLOC_NANO (reserved)           384.0M        1         reserved VM address space (unallocated)\nMach message                         8K        2 \nSQLite page cache                  768K        6 \nSTACK GUARD                       56.2M       47 \nStack                             40.4M       52 \nVM_ALLOCATE                      220.8M      517 \n__DATA                            39.8M      887 \n__DATA_CONST                      86.0M      892 \n__DATA_DIRTY                        86K       11 \n__FONT_DATA                        2352        1 \n__LINKEDIT                       710.0M       94 \n__OBJC_RO                         61.1M        1 \n__OBJC_RW                         1986K        1 \n__TEXT                             1.0G      909 \ndyld private memory               1816K       16 \nlibnetwork                         128K        8 \nmapped file                      265.4M      141 \nshared memory                       24K        3 \n===========                     =======  ======= \nTOTAL                              3.1G     3717 \nTOTAL, minus reserved VM space     2.8G     3717 \n",
  "legacyInfo" : {
  "threadTriggered" : {
    "queue" : "com.apple.main-thread"
  }
},
  "logWritingSignature" : "0ed9651156a9b7dc244b5e114e46abec3305e590",
  "trialInfo" : {
  "rollouts" : [
    {
      "rolloutId" : "62fe74515312cd4599bd3c80",
      "factorPackIds" : {
        "MYRIAD_BOOSTS" : "62fe74805312cd4599bd3c81"
      },
      "deploymentId" : 240000006
    },
    {
      "rolloutId" : "62c73fd17cce0d1b0bcb8a02",
      "factorPackIds" : {
        "SIRI_DIALOG_ASSETS" : "651482b98d79034dd899e383"
      },
      "deploymentId" : *********
    }
  ],
  "experiments" : [

  ]
}
}

Model: MacBookPro14,3, BootROM 529.140.2.0.0, 4 processors, Quad-Core Intel Core i7, 2.8 GHz, 16 GB, SMC 2.45f5
Graphics: Intel HD Graphics 630, Intel HD Graphics 630, Built-In
Graphics: Radeon Pro 555, Radeon Pro 555, PCIe, 2 GB
Display: Color LCD, 2880 x 1800 Retina, MirrorOff, Online
Display: G237HL, 1920 x 1080 (1080p FHD - Full High Definition), Main, MirrorOff, Online
Memory Module: BANK 0/DIMM0, 8 GB, LPDDR3, 2133 MHz, 0x80CE, 0x4B3445424533303445422D45474347202020
Memory Module: BANK 1/DIMM0, 8 GB, LPDDR3, 2133 MHz, 0x80CE, 0x4B3445424533303445422D45474347202020
AirPort: spairport_wireless_card_type_wifi (0x14E4, 0x173), Broadcom BCM43xx 1.0 (7.77.111.1 AirPortDriverBrcmNIC-1772.1)
AirPort: 
Bluetooth: Version (null), 0 services, 0 devices, 0 incoming serial ports
Network Service: Wi-Fi, AirPort, en0
USB Device: USB30Bus
USB Device: USB OPTICAL MOUSE
USB Device: Apple T1 Controller
Thunderbolt Bus: MacBook Pro, Apple Inc., 41.5
Thunderbolt Bus: MacBook Pro, Apple Inc., 41.5
