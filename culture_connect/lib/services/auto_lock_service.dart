import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/security_settings.dart';

/// Auto-lock service that monitors user activity and triggers lock screen
class AutoLockService with WidgetsBindingObserver {
  static final AutoLockService _instance = AutoLockService._internal();
  factory AutoLockService() => _instance;
  AutoLockService._internal();

  Timer? _inactivityTimer;
  DateTime _lastActivityTime = DateTime.now();
  bool _isLocked = false;
  bool _isInitialized = false;
  SecuritySettings? _currentSettings;
  VoidCallback? _onLockTriggered;
  StreamSubscription<SecuritySettings>? _settingsSubscription;

  /// Initialize the auto-lock service with settings stream
  void initialize(Stream<SecuritySettings> settingsStream,
      [VoidCallback? onLockTriggered]) {
    if (_isInitialized) return;

    _onLockTriggered = onLockTriggered;
    WidgetsBinding.instance.addObserver(this);
    _isInitialized = true;

    // Listen to security settings changes via stream
    _settingsSubscription = settingsStream.listen((settings) {
      _currentSettings = settings;
      _updateAutoLockTimer();
    });

    debugPrint('AutoLockService initialized');
  }

  /// Dispose the service
  void dispose() {
    if (!_isInitialized) return;

    _inactivityTimer?.cancel();
    _settingsSubscription?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    _isInitialized = false;
    _settingsSubscription = null;

    debugPrint('AutoLockService disposed');
  }

  /// Record user activity to reset the inactivity timer
  void recordActivity() {
    if (!_isInitialized || _isLocked) return;

    _lastActivityTime = DateTime.now();
    _updateAutoLockTimer();
  }

  /// Update the auto-lock timer based on current settings
  void _updateAutoLockTimer() {
    _inactivityTimer?.cancel();

    if (_currentSettings == null ||
        !_currentSettings!.autoLockEnabled ||
        _currentSettings!.autoLockTimeoutMinutes == -1) {
      return; // Auto-lock disabled or set to never
    }

    final timeout = _currentSettings!.timeoutDuration;
    if (timeout == Duration.zero) return;

    _inactivityTimer = Timer(timeout, () {
      if (!_isLocked && _isInitialized) {
        _triggerAutoLock();
      }
    });
  }

  /// Trigger the auto-lock functionality
  void _triggerAutoLock() {
    if (_isLocked || !_isInitialized) return;

    _isLocked = true;
    debugPrint('Auto-lock triggered after inactivity');

    // Trigger lock callback if available
    if (_onLockTriggered != null) {
      _onLockTriggered!();
    }
  }

  /// Manually lock the app
  void lockApp() {
    if (_isLocked || !_isInitialized) return;

    _triggerAutoLock();
  }

  /// Unlock the app (called from lock screen after successful authentication)
  void unlockApp() {
    if (!_isLocked) return;

    _isLocked = false;
    _lastActivityTime = DateTime.now();
    _updateAutoLockTimer();

    debugPrint('App unlocked');
  }

  /// Check if the app is currently locked
  bool get isLocked => _isLocked;

  /// Get time since last activity
  Duration get timeSinceLastActivity =>
      DateTime.now().difference(_lastActivityTime);

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (!_isInitialized || _currentSettings == null) return;

    switch (state) {
      case AppLifecycleState.resumed:
        // App came back to foreground
        if (_currentSettings!.lockOnAppBackground && !_isLocked) {
          // Check if we should auto-lock based on time away
          final timeAway = DateTime.now().difference(_lastActivityTime);
          if (timeAway >= _currentSettings!.timeoutDuration) {
            _triggerAutoLock();
          } else {
            recordActivity(); // Reset timer
          }
        }
        break;
      case AppLifecycleState.paused:
        // App went to background
        _lastActivityTime = DateTime.now();
        break;
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        // Handle other states if needed
        break;
    }
  }
}

/// Provider for the auto-lock service
final autoLockServiceProvider = Provider<AutoLockService>((ref) {
  final service = AutoLockService();

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for auto-lock service with proper initialization
final initializedAutoLockServiceProvider = Provider<AutoLockService>((ref) {
  final service = ref.watch(autoLockServiceProvider);
  return service;
});
